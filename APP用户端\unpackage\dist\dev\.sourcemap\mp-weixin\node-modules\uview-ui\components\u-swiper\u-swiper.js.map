{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-swiper/u-swiper.vue?5ce6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-swiper/u-swiper.vue?9ecf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-swiper/u-swiper.vue?db0d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-swiper/u-swiper.vue?deb7", "uni-app:///node_modules/uview-ui/components/u-swiper/u-swiper.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-swiper/u-swiper.vue?5802", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-swiper/u-swiper.vue?9e2b"], "names": ["name", "mixins", "data", "currentIndex", "watch", "current", "computed", "itemStyle", "style", "methods", "getItemType", "getSource", "uni", "change", "e", "pauseVideo", "video", "<PERSON><PERSON><PERSON>er", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,aAAa,8VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAAu1B,CAAgB,u2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC4F32B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA,eAgCA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;;EACAC;IACAC;MAAA;MACA;QACA;;QAEA;QACA;QACA;UACAC;UACA;QACA;QAEA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA,6FACAC;MACA;IACA;IACA;IACAC;MACA;MACA,IACAR,UACAS,SADAT;MAEA;MACA;MACA;IACA;IACA;IACAU;MACA;MACA;QACA;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAA8lD,CAAgB,kjDAAG,EAAC,C;;;;;;;;;;;ACAlnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-swiper/u-swiper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-swiper.vue?vue&type=template&id=7b038a67&scoped=true&\"\nvar renderjs\nimport script from \"./u-swiper.vue?vue&type=script&lang=js&\"\nexport * from \"./u-swiper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-swiper.vue?vue&type=style&index=0&id=7b038a67&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7b038a67\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-swiper/u-swiper.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swiper.vue?vue&type=template&id=7b038a67&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n    uSwiperIndicator: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper-indicator/u-swiper-indicator\" */ \"uview-ui/components/u-swiper-indicator/u-swiper-indicator.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.addUnit(_vm.height)\n  var g1 = _vm.$u.addUnit(_vm.radius)\n  var g2 = !_vm.loading ? _vm.$u.addUnit(_vm.height) : null\n  var g3 = !_vm.loading ? _vm.$u.addUnit(_vm.previousMargin) : null\n  var g4 = !_vm.loading ? _vm.$u.addUnit(_vm.nextMargin) : null\n  var l0 = !_vm.loading\n    ? _vm.__map(_vm.list, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var s0 = _vm.__get_style([_vm.itemStyle(index)])\n        var m0 = _vm.getItemType(item)\n        var g5 = m0 === \"image\" ? _vm.$u.addUnit(_vm.height) : null\n        var g6 = m0 === \"image\" ? _vm.$u.addUnit(_vm.radius) : null\n        var m1 = m0 === \"image\" ? _vm.getSource(item) : null\n        var m2 = _vm.getItemType(item)\n        var g7 = m2 === \"video\" ? _vm.$u.addUnit(_vm.height) : null\n        var m3 = m2 === \"video\" ? _vm.getSource(item) : null\n        var m4 = m2 === \"video\" ? _vm.getPoster(item) : null\n        var g8 =\n          m2 === \"video\"\n            ? _vm.showTitle && _vm.$u.test.object(item) && item.title\n            : null\n        var g9 =\n          _vm.showTitle &&\n          _vm.$u.test.object(item) &&\n          item.title &&\n          _vm.$u.test.image(_vm.getSource(item))\n        return {\n          $orig: $orig,\n          s0: s0,\n          m0: m0,\n          g5: g5,\n          g6: g6,\n          m1: m1,\n          m2: m2,\n          g7: g7,\n          m3: m3,\n          m4: m4,\n          g8: g8,\n          g9: g9,\n        }\n      })\n    : null\n  var s1 = _vm.__get_style([_vm.$u.addStyle(_vm.indicatorStyle)])\n  var g10 =\n    !_vm.loading && _vm.indicator && !_vm.showTitle ? _vm.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n        s1: s1,\n        g10: g10,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swiper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swiper.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-swiper\"\n\t\t:style=\"{\n\t\t\tbackgroundColor: bgColor,\n\t\t\theight: $u.addUnit(height),\n\t\t\tborderRadius: $u.addUnit(radius)\n\t\t}\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-swiper__loading\"\n\t\t\tv-if=\"loading\"\n\t\t>\n\t\t\t<u-loading-icon mode=\"circle\"></u-loading-icon>\n\t\t</view>\n\t\t<swiper\n\t\t\tv-else\n\t\t\tclass=\"u-swiper__wrapper\"\n\t\t\t:style=\"{\n\t\t\t\theight: $u.addUnit(height),\n\t\t\t}\"\n\t\t\t@change=\"change\"\n\t\t\t:circular=\"circular\"\n\t\t\t:interval=\"interval\"\n\t\t\t:duration=\"duration\"\n\t\t\t:autoplay=\"autoplay\"\n\t\t\t:current=\"current\"\n\t\t\t:currentItemId=\"currentItemId\"\n\t\t\t:previousMargin=\"$u.addUnit(previousMargin)\"\n\t\t\t:nextMargin=\"$u.addUnit(nextMargin)\"\n\t\t\t:acceleration=\"acceleration\"\n\t\t\t:displayMultipleItems=\"displayMultipleItems\"\n\t\t\t:easingFunction=\"easingFunction\"\n\t\t>\n\t\t\t<swiper-item\n\t\t\t\tclass=\"u-swiper__wrapper__item\"\n\t\t\t\tv-for=\"(item, index) in list\"\n\t\t\t\t:key=\"index\"\n\t\t\t>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper\"\n\t\t\t\t\t:style=\"[itemStyle(index)]\"\n\t\t\t\t>\n\t\t\t\t\t<!-- 在nvue中，image图片的宽度默认为屏幕宽度，需要通过flex:1撑开，另外必须设置高度才能显示图片 -->\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__image\"\n\t\t\t\t\t\tv-if=\"getItemType(item) === 'image'\"\n\t\t\t\t\t\t:src=\"getSource(item)\"\n\t\t\t\t\t\t:mode=\"imgMode\"\n\t\t\t\t\t\t@tap=\"clickHandler(index)\"\n\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\theight: $u.addUnit(height),\n\t\t\t\t\t\t\tborderRadius: $u.addUnit(radius)\n\t\t\t\t\t\t}\"\n\t\t\t\t\t></image>\n\t\t\t\t\t<video\n\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__video\"\n\t\t\t\t\t\tv-if=\"getItemType(item) === 'video'\"\n\t\t\t\t\t\t:id=\"`video-${index}`\"\n\t\t\t\t\t\t:enable-progress-gesture=\"false\"\n\t\t\t\t\t\t:src=\"getSource(item)\"\n\t\t\t\t\t\t:poster=\"getPoster(item)\"\n\t\t\t\t\t\t:title=\"showTitle && $u.test.object(item) && item.title ? item.title : ''\"\n\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\theight: $u.addUnit(height)\n\t\t\t\t\t\t}\"\n\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t@tap=\"clickHandler(index)\"\n\t\t\t\t\t></video>\n\t\t\t\t\t<text\n\t\t\t\t\t\tv-if=\"showTitle && $u.test.object(item) && item.title && $u.test.image(getSource(item))\"\n\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__title u-line-1\"\n\t\t\t\t\t>{{ item.title }}</text>\n\t\t\t\t</view>\n\t\t\t</swiper-item>\n\t\t</swiper>\n\t\t<view class=\"u-swiper__indicator\" :style=\"[$u.addStyle(indicatorStyle)]\">\n\t\t\t<slot name=\"indicator\">\n\t\t\t\t<u-swiper-indicator\n\t\t\t\t\tv-if=\"!loading && indicator && !showTitle\"\n\t\t\t\t\t:indicatorActiveColor=\"indicatorActiveColor\"\n\t\t\t\t\t:indicatorInactiveColor=\"indicatorInactiveColor\"\n\t\t\t\t\t:length=\"list.length\"\n\t\t\t\t\t:current=\"currentIndex\"\n\t\t\t\t\t:indicatorMode=\"indicatorMode\"\n\t\t\t\t></u-swiper-indicator>\n\t\t\t</slot>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * Swiper 轮播图\n\t * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用，\n\t * @tutorial https://www.uviewui.com/components/swiper.html\n\t * @property {Array}\t\t\tlist\t\t\t\t\t轮播图数据\n\t * @property {Boolean}\t\t\tindicator\t\t\t\t是否显示面板指示器（默认 false ）\n\t * @property {String}\t\t\tindicatorActiveColor\t指示器非激活颜色（默认 '#FFFFFF' ）\n\t * @property {String}\t\t\tindicatorInactiveColor\t指示器的激活颜色（默认 'rgba(255, 255, 255, 0.35)' ）\n\t * @property {String | Object}\tindicatorStyle\t\t\t指示器样式，可通过bottom，left，right进行定位\n\t * @property {String}\t\t\tindicatorMode\t\t\t指示器模式（默认 'line' ）\n\t * @property {Boolean}\t\t\tautoplay\t\t\t\t是否自动切换（默认 true ）\n\t * @property {String | Number}\tcurrent\t\t\t\t\t当前所在滑块的 index（默认 0 ）\n\t * @property {String}\t\t\tcurrentItemId\t\t\t当前所在滑块的 item-id ，不能与 current 被同时指定\n\t * @property {String | Number}\tinterval\t\t\t\t滑块自动切换时间间隔（ms）（默认 3000 ）\n\t * @property {String | Number}\tduration\t\t\t\t滑块切换过程所需时间（ms）（默认 300 ）\n\t * @property {Boolean}\t\t\tcircular\t\t\t\t播放到末尾后是否重新回到开头（默认 false ）\n\t * @property {String | Number}\tpreviousMargin\t\t\t前边距，可用于露出前一项的一小部分，nvue和支付宝不支持（默认 0 ）\n\t * @property {String | Number}\tnextMargin\t\t\t\t后边距，可用于露出后一项的一小部分，nvue和支付宝不支持（默认 0 ）\n\t * @property {Boolean}\t\t\tacceleration\t\t\t当开启时，会根据滑动速度，连续滑动多屏，支付宝不支持（默认 false ）\n\t * @property {Number}\t\t\tdisplayMultipleItems\t同时显示的滑块数量，nvue、支付宝小程序不支持（默认 1 ）\n\t * @property {String}\t\t\teasingFunction\t\t\t指定swiper切换缓动动画类型， 只对微信小程序有效（默认 'default' ）\n\t * @property {String}\t\t\tkeyName\t\t\t\t\tlist数组中指定对象的目标属性名（默认 'url' ）\n\t * @property {String}\t\t\timgMode\t\t\t\t\t图片的裁剪模式（默认 'aspectFill' ）\n\t * @property {String | Number}\theight\t\t\t\t\t组件高度（默认 130 ）\n\t * @property {String}\t\t\tbgColor\t\t\t\t\t背景颜色（默认 \t'#f3f4f6' ）\n\t * @property {String | Number}\tradius\t\t\t\t\t组件圆角，数值或带单位的字符串（默认 4 ）\n\t * @property {Boolean}\t\t\tloading\t\t\t\t\t是否加载中（默认 false ）\n\t * @property {Boolean}\t\t\tshowTitle\t\t\t\t是否显示标题，要求数组对象中有title属性（默认 false ）\n\t * @event {Function(index)}\tclick\t点击轮播图时触发\tindex：点击了第几张图片，从0开始\n\t * @event {Function(index)}\tchange\t轮播图切换时触发(自动或者手动切换)\tindex：切换到了第几张图片，从0开始\n\t * @example\t<u-swiper :list=\"list4\" keyName=\"url\" :autoplay=\"false\"></u-swiper>\n\t */\n\texport default {\n\t\tname: 'u-swiper',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentIndex: 0\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tcurrent(val, preVal) {\n\t\t\t\tif(val === preVal) return;\n\t\t\t\tthis.currentIndex = val; // 和上游数据关联上\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\titemStyle() {\n\t\t\t\treturn index => {\n\t\t\t\t\tconst style = {}\n\t\t\t\t\t// #ifndef APP-NVUE || MP-TOUTIAO\n\t\t\t\t\t// 左右流出空间的写法不支持nvue和头条\n\t\t\t\t\t// 只有配置了此二值，才加上对应的圆角，以及缩放\n\t\t\t\t\tif (this.nextMargin && this.previousMargin) {\n\t\t\t\t\t\tstyle.borderRadius = uni.$u.addUnit(this.radius)\n\t\t\t\t\t\tif (index !== this.currentIndex) style.transform = 'scale(0.92)'\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n      getItemType(item) {\n        if (typeof item === 'string') return uni.$u.test.video(this.getSource(item)) ? 'video' : 'image'\n        if (typeof item === 'object' && this.keyName) {\n          if (!item.type) return uni.$u.test.video(this.getSource(item)) ? 'video' : 'image'\n          if (item.type === 'image') return 'image'\n          if (item.type === 'video') return 'video'\n          return 'image'\n        }\n      },\n\t\t\t// 获取目标路径，可能数组中为字符串，对象的形式，额外可指定对象的目标属性名keyName\n\t\t\tgetSource(item) {\n\t\t\t\tif (typeof item === 'string') return item\n\t\t\t\tif (typeof item === 'object' && this.keyName) return item[this.keyName]\n\t\t\t\telse uni.$u.error('请按格式传递列表参数')\n\t\t\t\treturn ''\n\t\t\t},\n\t\t\t// 轮播切换事件\n\t\t\tchange(e) {\n\t\t\t\t// 当前的激活索引\n\t\t\t\tconst {\n\t\t\t\t\tcurrent\n\t\t\t\t} = e.detail\n\t\t\t\tthis.pauseVideo(this.currentIndex)\n\t\t\t\tthis.currentIndex = current\n\t\t\t\tthis.$emit('change', e.detail)\n\t\t\t},\n\t\t\t// 切换轮播时，暂停视频播放\n\t\t\tpauseVideo(index) {\n\t\t\t\tconst lastItem = this.getSource(this.list[index])\n\t\t\t\tif (uni.$u.test.video(lastItem)) {\n\t\t\t\t\t// 当视频隐藏时，暂停播放\n\t\t\t\t\tconst video = uni.createVideoContext(`video-${index}`, this)\n\t\t\t\t\tvideo.pause()\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 当一个轮播item为视频时，获取它的视频海报\n\t\t\tgetPoster(item) {\n\t\t\t\treturn typeof item === 'object' && item.poster ? item.poster : ''\n\t\t\t},\n\t\t\t// 点击某个item\n\t\t\tclickHandler(index) {\n\t\t\t\tthis.$emit('click', index)\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-swiper {\n\t\t@include flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&__wrapper {\n\t\t\tflex: 1;\n\n\t\t\t&__item {\n\t\t\t\tflex: 1;\n\n\t\t\t\t&__wrapper {\n\t\t\t\t\t@include flex;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\ttransition: transform 0.3s;\n\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t&__image {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\n\t\t\t\t\t&__video {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\n\t\t\t\t\t&__title {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.3);\n\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tpadding: 12rpx 24rpx;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__indicator {\n\t\t\tposition: absolute;\n\t\t\tbottom: 10px;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swiper.vue?vue&type=style&index=0&id=7b038a67&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swiper.vue?vue&type=style&index=0&id=7b038a67&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756169663710\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}