{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/add_address.vue?90c4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/add_address.vue?22a0", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/add_address.vue?bc1e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/add_address.vue?4ecb", "uni-app:///user/add_address.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/add_address.vue?3024", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/add_address.vue?ebeb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "flag", "loading", "showCity", "menpai", "form", "userName", "mobile", "address", "addressInfo", "houseNumber", "city", "cityIds", "lng", "lat", "sex", "status", "provinceId", "cityId", "areaId", "columnsCity", "onLoad", "methods", "goMap", "uni", "scope", "success", "console", "that", "fail", "confirmCity", "getCity", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "index", "picker", "<PERSON><PERSON><PERSON><PERSON>", "icon", "title", "findduration", "phoneReg", "duration", "key", "userId", "subForm", "uniacid", "setTimeout", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkD92B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,cACA;MAAA;MACA;MAAA;MACA;MAAA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MAEAC;QACAC;QACAC;UACAF;YACAE;cACAC;cACAC;cACAA;cACAA;cACAA;cACAA;YACA;UACA;QACA;QACAC;UACAF;QACA;MACA;IAcA;IACAG;MAAA;MACA;MACA;MACA;QAAA;QACA;MACA;MACA;QAAA;QACA;MACA;QAAA;MAAA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QAAA;QACA;QACA;UACA;YAAA;YACA;YACA;cACA;gBACA;gBACA;cAEA;YACA;UACA;QACA;MACA;QACAJ;MACA;IACA;IACAK;MAAA;MACA,IACAC,cAGAC,EAHAD;QACAE,QAEAD,EAFAC;QAAA,YAEAD,EADAE;QAAAA;MAEA;QAAA;QACA;UACA;YAAA;YACAA;YACA;YACA;cACA;gBACAA;gBACA;gBACAT;cACA;YACA;UACA;QACA;MACA;QAAA;QACA;UACA;YACAS;YACA;YACAT;UACA;QACA;MACA;IACA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAb;kBACAc;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAjB;kBACAc;kBACAC;kBACAG;gBACA;gBAAA;cAAA;gBAAA,eAGA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAnB;kBACAc;kBACAC;kBACAG;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAlB;kBACAc;kBACAC;kBACAG;gBACA;gBAAA;cAAA;gBAGA;gBACAE;gBACAjB;gBACAA;gBACA;gBACAkB;kBACArC;kBACAC;kBACAU;kBACAR;kBACAO;kBACAN;kBAAA;kBACA;kBACAF;kBACA;kBACAI;kBACAD;kBACAN;kBACAU;kBACAF;kBACAC;kBACA;kBACA8B;kBAEA;kBACAxC;gBACA;gBACA;kBACAkB;oBACAc;oBACAC;oBACAG;kBACA;kBACAK;oBACAvB;sBAAAwB;oBAAA;kBACA;gBACA;kBACAxB;oBACAc;oBACAC;oBACAG;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5QA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/add_address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/add_address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add_address.vue?vue&type=template&id=99ba6ec0&scoped=true&\"\nvar renderjs\nimport script from \"./add_address.vue?vue&type=script&lang=js&\"\nexport * from \"./add_address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99ba6ec0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/add_address.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=template&id=99ba6ec0&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-switch/u-switch\" */ \"uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCity = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.form.sex = 1\n    }\n    _vm.e3 = function ($event) {\n      _vm.form.sex = 2\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-picker :show=\"showCity\" ref=\"uPicker\" :loading=\"loading\" :columns=\"columnsCity\" @change=\"changeHandler\"\n\t\t\tkeyName=\"title\" @cancel=\"showCity = false\" @confirm=\"confirmCity\" v-if=\"flag\"></u-picker>\n\t\t<view class=\"top\">个人信息隐私信息完全保密</view>\n\t\t<view class=\"main\">\n\t\t\t<view class=\"main_item \" @tap=\"goMap\">\n\t\t\t\t<view class=\"name\">服务地址</view>\n\t\t\t\t<view class=\"address\">\n\t\t\t\t\t<span>{{form.address}}</span>\n\t\t\t\t</view>\n\t\t\t\t<image src=\"../static/images/position.png\" mode=\"\"></image>\n\t\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"name\">所在区域</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.city\" placeholder=\"请选择所在区域\" disabled @click=\"showCity = true\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"name\">门牌号</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.houseNumber\" placeholder=\"请输入详细地址，如7栋4单元18a\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"name\">联系人</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.userName\" placeholder=\"请输入姓名\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"name\">性别</view>\n\t\t\t\t<view class=\"box\">\n\t\t\t\t\t<view class=\"box_item\"\n\t\t\t\t\t\t:style=\"form.sex == 1?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''\"\n\t\t\t\t\t\t@tap=\"form.sex = 1\">先生</view>\n\t\t\t\t\t<view class=\"box_item\"\n\t\t\t\t\t\t:style=\"form.sex == 2?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''\"\n\t\t\t\t\t\t@tap=\"form.sex = 2\">女士</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"name\">手机号码</view>\n\t\t\t\t<input type=\"tel\" v-model=\"form.mobile\" placeholder=\"请输入手机号码\">\n\t\t\t</view>\n\t\t\t<view class=\"main_item last\">\n\t\t\t\t<view class=\"name\">设为默认地址</view>\n\t\t\t\t<u-switch v-model=\"form.status\" activeColor=\"#2E80FE\"></u-switch>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"btn\" @click=\"SaveAddress\">保存</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tflag: false,\n\t\t\t\tloading: false,\n\t\t\t\tshowCity: false,\n\t\t\t\tmenpai: '',\n\t\t\t\tform: {\n\t\t\t\t\tuserName: '',\n\t\t\t\t\tmobile: '',\n\t\t\t\t\taddress: '点击选择服务地址',\n\t\t\t\t\taddressInfo: '',\n\t\t\t\t\thouseNumber: '',\n\t\t\t\t\tcity: '',\n\t\t\t\t\tcityIds: [],\n\t\t\t\t\tlng: '',\n\t\t\t\t\tlat: '',\n\t\t\t\t\tsex: 1,\n\t\t\t\t\tstatus: false,\n\t\t\t\t\tprovinceId: 0,\n\t\t\t\t\tcityId: 0,\n\t\t\t\t\tareaId: 0\n\t\t\t\t},\n\t\t\t\tcolumnsCity: [\n\t\t\t\t\t[], // Province\n\t\t\t\t\t[], // City\n\t\t\t\t\t[]  // Area\n\t\t\t\t],\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getCity(0)\n\t\t},\n\t\tmethods: {\n\t\t\tgoMap() {\n\t\t\t\tlet that = this\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.authorize({\n\t\t\t\t\tscope: 'scope.userLocation',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tuni.chooseLocation({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\t\tthat.form.address = res.name\n\t\t\t\t\t\t\t\tthat.form.addressInfo = res.address\n\t\t\t\t\t\t\t\tthat.form.lng = res.longitude\n\t\t\t\t\t\t\t\tthat.form.lat = res.latitude\n\t\t\t\t\t\t\t\tthat.form.cityId = res.id\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail(err) {\n\t\t\t\t\t\tconsole.error(err)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP\n\t\t\t\tuni.chooseLocation({\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\tthat.form.address = res.name\n\t\t\t\t\t\tthat.form.addressInfo = res.address\n\t\t\t\t\t\tthat.form.lng = res.longitude\n\t\t\t\t\t\tthat.form.lat = res.latitude\n\t\t\t\t\t\tthat.form.cityId = res.id\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tconfirmCity(Array) {\n\t\t\t\t// Map selected values to titles and IDs\n\t\t\t\tconst selectedValues = Array.value\n\t\t\t\tconst titles = selectedValues.map((item, index) => {\n\t\t\t\t\treturn item?.title || this.columnsCity[index][0]?.title || ''\n\t\t\t\t})\n\t\t\t\tconst ids = selectedValues.map((item, index) => {\n\t\t\t\t\treturn item?.id || this.columnsCity[index][0]?.id || 0\n\t\t\t\t}).filter(id => id !== null && id !== undefined)\n\n\t\t\t\tthis.form.city = titles.join('-')\n\t\t\t\t// Set cityIds as nested array [[provinceId, cityId, areaId]]\n\t\t\t\tthis.form.cityIds = ids.length >= 3 ? [[ids[0], ids[1], ids[2]]] : [[0, 0, 0]]\n\t\t\t\t// Set individual IDs\n\t\t\t\tthis.form.provinceId = ids[0] || 0\n\t\t\t\tthis.form.cityId = ids[1] || 0\n\t\t\t\tthis.form.areaId = ids[2] || 0\n\t\t\t\tthis.showCity = false\n\t\t\t},\n\t\t\tgetCity(e) {\n\t\t\t\tthis.$api.service.getCity(e).then(res => {\n\t\t\t\t\tthis.columnsCity[0] = res\n\t\t\t\t\tif (res[0]?.id) {\n\t\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\n\t\t\t\t\t\t\tthis.columnsCity[1] = res1\n\t\t\t\t\t\t\tif (res1[0]?.id) {\n\t\t\t\t\t\t\t\tthis.$api.service.getCity(res1[0].id).then(res2 => {\n\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = res2\n\t\t\t\t\t\t\t\t\tthis.flag = true\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('Failed to fetch city data:', err)\n\t\t\t\t})\n\t\t\t},\n\t\t\tchangeHandler(e) {\n\t\t\t\tconst {\n\t\t\t\t\tcolumnIndex,\n\t\t\t\t\tindex,\n\t\t\t\t\tpicker = this.$refs.uPicker\n\t\t\t\t} = e\n\t\t\t\tif (columnIndex === 0) {\n\t\t\t\t\tif (this.columnsCity[0][index]?.id) {\n\t\t\t\t\t\tthis.$api.service.getCity(this.columnsCity[0][index].id).then(res => {\n\t\t\t\t\t\t\tpicker.setColumnValues(1, res)\n\t\t\t\t\t\t\tthis.columnsCity[1] = res\n\t\t\t\t\t\t\tif (res[0]?.id) {\n\t\t\t\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\n\t\t\t\t\t\t\t\t\tpicker.setColumnValues(2, res1)\n\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = res1\n\t\t\t\t\t\t\t\t\t\tconsole.log(res1)\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} else if (columnIndex === 1) {\n\t\t\t\t\tif (this.columnsCity[1][index]?.id) {\n\t\t\t\t\t\tthis.$api.service.getCity(this.columnsCity[1][index].id).then(res => {\n\t\t\t\t\t\t\tpicker.setColumnValues(2, res)\n\t\t\t\t\t\t\tthis.columnsCity[2] = res\n\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync SaveAddress() {\n\t\t\t\tif (this.form.address === '点击选择服务地址') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写完整提交',\n\t\t\t\t\t\tfindduration: 1500\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet phoneReg = /^1[3456789]\\d{9}$/\n\t\t\t\tif (!phoneReg.test(this.form.mobile)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写正确的手机号',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tfor (let key of ['userName', 'mobile', 'address', 'houseNumber', 'city']) {\n\t\t\t\t\tif (this.form[key] === '') {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '请填写完整提交',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!this.form.cityIds.length || this.form.cityIds[0].includes(0)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择所在区域',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t// Prepare form data for API\n\t\t\t\tlet userId= uni.getStorageSync('userInfo')\n\t\t\t\tconsole.log(userId)\n\t\t\t\tconsole.log(this.form)\n\t\t\t\t// console.log(JSON.parse(userId))\n\t\t\t\tlet subForm = {\n\t\t\t\t\taddress: this.form.address,\n\t\t\t\t\taddressInfo: this.form.addressInfo,\n\t\t\t\t\tareaId: this.form.areaId,\n\t\t\t\t\tcity: this.form.city,\n\t\t\t\t\tcityId: this.form.cityId,\n\t\t\t\t\tcityIds: this.form.cityIds, // [[provinceId, cityId, areaId]]\n\t\t\t\t\t// createTime: 0,\n\t\t\t\t\thouseNumber: this.form.houseNumber,\n\t\t\t\t\t// id: 0,\n\t\t\t\t\tlat: this.form.lat,\n\t\t\t\t\tlng: this.form.lng,\n\t\t\t\t\tmobile: this.form.mobile,\n\t\t\t\t\tprovinceId: this.form.provinceId,\n\t\t\t\t\tsex: this.form.sex,\n\t\t\t\t\tstatus: this.form.status ? 0 : 1,\n\t\t\t\t\t// top: 0,\n\t\t\t\t\tuniacid: this.form.uniacid,\n\t\t\t\t\t\n\t\t\t\t\t// userId:userId.userId,\n\t\t\t\t\tuserName: this.form.userName\n\t\t\t\t}\n\t\t\t\tthis.$api.mine.addressAdd(subForm).then(res => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '提交成功',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t})\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack({ delta: 1 })\n\t\t\t\t\t}, 1000)\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: err.msg || '提交失败',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\theight: 100vh;\n\t\tbackground-color: #fff;\n\n\t\t.top {\n\t\t\twidth: 750rpx;\n\t\t\theight: 58rpx;\n\t\t\tbackground: #FFF7F1;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #FE921B;\n\t\t\tline-height: 58rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.btn {\n\t\t\tmargin: 0 auto;\n\t\t\tmargin-top: 88rpx;\n\t\t\twidth: 690rpx;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.main {\n\t\t\tpadding: 0 30rpx;\n\n\t\t\t.main_item {\n\t\t\t\tpadding: 40rpx 0;\n\t\t\t\tborder-bottom: 2rpx solid #E9E9E9;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tposition: relative;\n\n\t\t\t\t.name {\n\t\t\t\t\tmin-width: 112rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tmargin-right: 40rpx;\n\t\t\t\t}\n\n\t\t\t\t.address {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #ADADAD;\n\n\t\t\t\t\t.details {\n\t\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #ADADAD;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 23rpx;\n\t\t\t\t\theight: 27rpx;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 0;\n\t\t\t\t\ttop: 46rpx;\n\t\t\t\t}\n\n\t\t\t\tinput {\n\t\t\t\t\twidth: 450rpx;\n\t\t\t\t}\n\n\t\t\t\t.box {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.box_item {\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t\twidth: 88rpx;\n\t\t\t\t\t\theight: 50rpx;\n\t\t\t\t\t\tbackground: #FFFFFF;\n\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\tborder: 2rpx solid #EDEDED;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #ADADAD;\n\t\t\t\t\t\tline-height: 46rpx;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.last {\n\t\t\t\tjustify-content: space-between;\n\n\t\t\t\t.name {\n\t\t\t\t\twidth: 170rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756176210416\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}