{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbar.vue?332d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbar.vue?a533", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbar.vue?41fb", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbar.vue?8cae", "uni-app:///components/tabbar.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbar.vue?54ac", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/components/tabbar.vue?b4a1"], "names": ["name", "props", "cur", "type", "default", "data", "activeColor", "inactiveColor", "tmplIds", "tabbarConfig", "icon", "value", "path", "computed", "primaryColor", "subColor", "configInfo", "commonOptions", "activeIndex", "mounted", "console", "methods", "isActive", "changeTab", "uni", "url", "fail", "handleSubscription", "success", "title", "content", "cancelText", "confirmText", "confirmColor", "withSubscriptions", "updateTabbarHeight", "query", "select", "boundingClientRect", "key", "val", "exec"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACsBz2B;AAGA;AAAA;AAAA,eAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC,UACA,+CACA,+CACA,8CACA;MACAC,eACA;QACAT;QACAU;QACAC;QACAC;MACA,GACA;QACAZ;QACAU;QACAC;QACAC;MACA,GACA;QACAZ;QACAU;QACAC;QACAC;MACA,GACA;QACAZ;QACAU;QACAC;QACAC;MACA,GAEA;QACAZ;QACAU;QACAC;QACAC;MACA;IAEA;EACA;EAEAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACA;IACAC;EACA;EACAC,yCACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MAEA;QAAA;MAAA;MACA;QACAH;;QAEA;QACA;;QAEA;QACAI;UACAC;UACAC;YACAN;UACA;QACA;MACA;IACA;IACA;IACAO;MAAA;MACA;MACA;MACA;MAEA;QACAP;QAEAI;UACAhB;UACAoB;YACAR;YACA;YACA;cAAA;YAAA;YACA;cACAI;gBACAK;gBACAC;gBACAC;gBACAC;gBACAC;gBACAL;kBACA;kBACAJ;kBACA;oBACAA;sBACAU;oBACA;kBACA;oBACA;oBACAV;kBACA;gBACA;cACA;YACA;UACA;UACAE;YACAN;UACA;QACA;MAEA;IACA;IACA;IACAe;MAAA;MACA;MACAC,MACAC,yBACAC;QACA;UACA;UACA;UACA;;UAEA;UACA;UACAtB;UACAA;UACA;YACAuB;YACAC;UACA;UAEApB;QACA;MACA,GACAqB;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7LA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tabbar.vue?vue&type=template&id=852a8b4e&scoped=true&\"\nvar renderjs\nimport script from \"./tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tabbar.vue?vue&type=style&index=0&id=852a8b4e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"852a8b4e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabbar.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=template&id=852a8b4e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabbarConfig.length\n  var l0 = _vm.__map(_vm.tabbarConfig, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.isActive(item.value)\n    var m1 = _vm.isActive(item.value)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"custom-tabbar fixed flex-center bg-base border-top\">\n\t\t<view\n\t\t\tv-for=\"(item, index) in tabbarConfig\"\n\t\t\t:key=\"item.value\"\n\t\t\**********=\"changeTab(item.value)\"\n\t\t\tclass=\"flex-center flex-column\"\n\t\t\t:style=\"{ width: 100 / tabbarConfig.length + '%', color: isActive(item.value) ? activeColor : inactiveColor }\"\n\t\t>\n\t\t\t<view class=\"icon-wrapper\">\n\t\t\t\t<u-icon\n\t\t\t\t\t:name=\"item.icon\"\n\t\t\t\t\t:color=\"isActive(item.value) ? activeColor : inactiveColor\"\n\t\t\t\t\tsize=\"28\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t\t<view class=\"tab-text\">{{ item.name }}</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tmapState,\n\t\tmapMutations\n\t} from \"vuex\";\n\n\texport default {\n\t\tname: 'CustomTabbar',\n\t\tprops: {\n\t\t\tcur: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: '0'\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tactiveColor: '#599eff',\n\t\t\t\tinactiveColor: '#666',\n\t\t\t\ttmplIds: [\n\t\t\t\t\t'vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\ttabbarConfig: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '首页',\n\t\t\t\t\t\ticon: 'home-fill',\n\t\t\t\t\t\tvalue: 0,\n\t\t\t\t\t\tpath: '/pages/service'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '分类',\n\t\t\t\t\t\ticon: 'grid-fill',\n\t\t\t\t\t\tvalue: 1,\n\t\t\t\t\t\tpath: '/pages/technician'\n\t\t\t\t\t},\r\n\t\t\t\t\t  {\r\n\t\t\t\t\t                name: '快速下单',\r\n\t\t\t\t\t                icon: '/static/mine/default_user.png',\r\n\t\t\t\t\t                value: 3,\r\n\t\t\t\t\t                path: '/pages/fast'\r\n\t\t\t\t\t            },\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '购物车',\n\t\t\t\t\t\ticon: 'shopping-cart-fill',\n\t\t\t\t\t\tvalue: 2,\n\t\t\t\t\t\tpath: '/pages/order'\n\t\t\t\t\t},\n\t\t\t\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '我的',\n\t\t\t\t\t\ticon: 'account-fill',\n\t\t\t\t\t\tvalue: 4,\n\t\t\t\t\t\tpath: '/pages/mine'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\tprimaryColor: (state) => state.config.configInfo.primaryColor,\n\t\t\t\tsubColor: (state) => state.config.configInfo.subColor,\n\t\t\t\tconfigInfo: (state) => state.config.configInfo,\n\t\t\t\tcommonOptions: (state) => state.user.commonOptions,\n\t\t\t\tactiveIndex: (state) => state.order.activeIndex,\n\t\t\t}),\n\t\t},\n\t\tmounted() {\n\t\t\tthis.updateTabbarHeight();\n\t\t\tconsole.log('Current tab:', this.cur);\n\t\t},\n\t\tmethods: {\n\t\t\t...mapMutations([\"updateConfigItem\"]),\n\t\t\t// 检查是否为激活状态的tab\n\t\t\tisActive(value) {\n\t\t\t\treturn String(value) === String(this.cur);\n\t\t\t},\n\t\t\t// 切换tab\n\t\t\tchangeTab(value) {\n\t\t\t\tif (this.isActive(value)) return;\n\t\t\t\t\n\t\t\t\tconst targetTab = this.tabbarConfig.find(item => String(item.value) === String(value));\n\t\t\t\tif (targetTab) {\n\t\t\t\t\tconsole.log('Navigating to:', targetTab.path);\n\t\t\t\t\t\n\t\t\t\t\t// 处理订阅消息\n\t\t\t\t\tthis.handleSubscription();\n\t\t\t\t\t\n\t\t\t\t\t// 跳转页面\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: targetTab.path,\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error(\"Navigation failed:\", err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 处理订阅消息\n\t\t\thandleSubscription() {\n\t\t\t\tconst userId = uni.getStorageSync('userId');\n\t\t\t\tconst hasCanceledSubscription = uni.getStorageSync('hasCanceledSubscription');\n\t\t\t\tconst hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');\n\t\t\t\t\n\t\t\t\tif (userId && !hasCanceledSubscription) {\n\t\t\t\t\tconsole.log('Requesting subscription for user:', userId);\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t\t// Check if any of the template IDs were rejected\n\t\t\t\t\t\t\tconst hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');\n\t\t\t\t\t\t\tif (hasRejection && !hasShownModal) {\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\t\tcontent: \"您已关闭消息订阅，建议点击'通知管理'开启，方便及时接收师傅的服务通知。\",\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\tconfirmText: '去开启',\n\t\t\t\t\t\t\t\t\tconfirmColor: '#007AFF',\n\t\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\t\t// Set flag to prevent showing modal again\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasShownSubscriptionModal', true);\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\t\twithSubscriptions: true\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t} else if (modalRes.cancel) {\n\t\t\t\t\t\t\t\t\t\t\t// Set flag in storage when user cancels\n\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasCanceledSubscription', true);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 计算tabbar高度并更新\n\t\t\tupdateTabbarHeight() {\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\n\t\t\t\tquery\n\t\t\t\t\t.select('.custom-tabbar')\n\t\t\t\t\t.boundingClientRect(data => {\n\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\t\t\tconst tabbarHeight = data.height;\n\t\t\t\t\t\t\tconst windowHeight = systemInfo.windowHeight;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新配置信息\n\t\t\t\t\t\t\tlet configInfo = JSON.parse(JSON.stringify(this.configInfo));\n\t\t\t\t\t\t\tconfigInfo.curSysHeight = windowHeight - tabbarHeight - (configInfo.navBarHeight || 0);\n\t\t\t\t\t\t\tconfigInfo.tabbarHeight = tabbarHeight;\n\t\t\t\t\t\t\tthis.updateConfigItem({\n\t\t\t\t\t\t\t\tkey: \"configInfo\",\n\t\t\t\t\t\t\t\tval: configInfo,\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log(\"Tabbar height updated:\", tabbarHeight, \"curSysHeight:\", configInfo.curSysHeight);\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.exec();\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.custom-tabbar {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 120rpx;\n\t\tpadding-bottom: calc(env(safe-area-inset-bottom) / 2);\n\t\tbackground-color: #fff;\n\t\tz-index: 1000;\n\t\tborder-top: 1px solid #eee;\n\t}\n\t.tab-text {\n\t\tfont-size: 22rpx;\n\t\tmargin-top: 5rpx;\n\t\tline-height: 32rpx;\n\t}\n\t.flex-center {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.flex-column {\n\t\tflex-direction: column;\n\t}\n\t.bg-base {\n\t\tbackground-color: #fff;\n\t}\n\t.icon-wrapper {\n\t\tposition: relative;\n\t}\n\t.badge {\n\t\tposition: absolute;\n\t\ttop: -5rpx;\n\t\tright: -8rpx;\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tbackground-color: #E41F19;\n\t\tcolor: #fff;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-size: 20rpx;\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=style&index=0&id=852a8b4e&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tabbar.vue?vue&type=style&index=0&id=852a8b4e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756169659800\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}