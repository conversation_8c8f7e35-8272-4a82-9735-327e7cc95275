{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Cashier.vue?ee7a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Cashier.vue?c690", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Cashier.vue?f277", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Cashier.vue?2aee", "uni-app:///user/Cashier.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Cashier.vue?0590", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/Cashier.vue?b6be"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentIndex", "id", "infoyouhuij", "price", "showYh", "couponlist", "nocouponlist", "couType", "confirmCou", "couponNum", "goods_id", "type", "tmplIds", "computed", "allprice", "methods", "getCurrentPlatform", "getList", "console", "status", "serviceId", "payPrice", "res", "item", "arr", "arr1", "chooseNotyh", "chooseYh", "chooseItemyh", "uni", "icon", "title", "e", "confirmPay", "orderId", "couponId", "handleAppWechatPay", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "setTimeout", "url", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "dingyue", "provider", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuG12B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAKA;MAKA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OACA;kBACAC;kBACAC;kBACAC;gBACA;kBACA;kBACA;kBACAC;oBACAC;oBACA;sBACAC;oBACA;sBACAC;oBACA;kBACA;kBACAP;kBACAM;kBACAC;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACAH;QACA;MACA;IACA;IACAI;MAAA;MACA;IACA;IACAC;MACA;QACA;QACA;UAAA;UACA;YACAC;cACAC;cACAC;YACA;YACA;UACA;QACA;QACA;UACAC;QACA;QACAT;QACA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IACAU;MAAA;MACA;MACA;MACAf;MAEA;QACAgB;QACAC;QACAxB;QACA;MACA;QACA;UACAkB;YACAE;YACAD;UACA;QACA;UACAZ;UACA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAkB;MAAA;MACAlB;MACAW;QACA;QACAQ;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEACA;QACA1B;QACAW;UACAE;UACAD;QACA;QACA;QACAe;UACAhB;YACAiB;UACA;QACA;MACA,+DACA;QACA5B;QACA;UACAW;YACAE;YACAD;UACA;QACA;UACAD;YACAE;YACAD;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAiB;MAAA;MACA;QACAC;QAAA;QACAC;QACAT;QACAU;QACAC;MACA;MACAjC;MACAW;QACA;QACAmB;QACAC;QACAT;QACAC;QACAS;QACAC;QACAC;QACAC;UACA;UACAnC;UACAW;YACAE;YACAD;UACA;UACA;UACAe;YACAhB;cACAiB;YACA;UACA;QACA;QACAQ;UACA;UACApC;UACAA;UACA;YACAW;cACAE;cACAD;YACA;UACA;YACAD;cACAE;cACAD;YACA;UACA;UACAZ;UACAW;YACAE;YACAD;UACA;QACA;MACA;IACA;IACAyB;MAAA;MAEA1B;QACA2B;QACA5C;QACAyC;UACA;YAAA;UAAA;UACA;YACAxB;cACAE;cACAD;YACA;UACA;QACA;QACAwB;UACA;UACA;UACA;UACA;QAAA;MAEA;IAEA,EACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAG;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAvC;cACAA;cACA;cACA;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrYA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/Cashier.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/Cashier.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Cashier.vue?vue&type=template&id=5b297b6d&scoped=true&\"\nvar renderjs\nimport script from \"./Cashier.vue?vue&type=script&lang=js&\"\nexport * from \"./Cashier.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Cashier.vue?vue&type=style&index=0&id=5b297b6d&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b297b6d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/Cashier.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Cashier.vue?vue&type=template&id=5b297b6d&scoped=true&\"", "var components\ntry {\n  components = {\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.couponlist.length == 0 && _vm.nocouponlist.length == 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.currentIndex = 0\n    }\n    _vm.e1 = function ($event) {\n      _vm.showYh = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Cashier.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Cashier.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"time\"><text>支付剩余时间</text> <u-count-down :time=\"15 * 60 * 1000\" format=\"mm:ss\"></u-count-down>\n\t\t</view>\n\t\t<span class=\"price\"><span>￥</span>{{allprice}}</span>\n\t\t<view class=\"payCard\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<image src=\"../static/svg/weixinfang.svg\" mode=\"aspectFill\"></image>\n\t\t\t\t<text>微信支付</text>\n\t\t\t</view>\n\t\t\t<view class=\"choose\" :style=\"currentIndex == 0?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t@click=\"currentIndex = 0\">\n\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"payCard\" @click=\"chooseYh\">\n\t\t\t<view class=\"left\">\n\t\t\t\t优惠券\n\t\t\t</view>\n\t\t\t<view class=\"right\">\n\t\t\t\t<text v-if=\"!couType\">{{couponNum}}张可用</text>\n\t\t\t\t<text v-else>{{confirmCou.title}} -{{confirmCou.discount}}</text>\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" size=\"14\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- <view class=\"payCard\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<image src=\"../static/svg/qianbao.svg\" mode=\"aspectFill\"></image>\n\t\t\t\t<text>余额支付</text>\n\t\t\t</view>\n\t\t\t<view class=\"choose\" :style=\"currentIndex == 1?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t@click=\"currentIndex = 1\">\n\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon>\n\t\t\t</view>\n\t\t</view> -->\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn\" @click=\"confirmPay\">确认支付</view>\n\t\t</view>\n\t\t<!-- 优惠券弹出框 -->\n\t\t<view class=\"choose_yh\" :style=\"showYh?'':'height:0'\">\n\n\t\t\t<view class=\"head\">优惠券</view>\n\t\t\t<view class=\"close\" @click=\"showYh = false\">\n\t\t\t\t<image src=\"../static/images/9397.png\" mode=\"\"></image>\n\t\t\t</view>\n\t\t\t<u-empty mode=\"coupon\" icon=\"http://cdn.uviewui.com/uview/empty/coupon.png\"\n\t\t\t\tv-if=\"couponlist.length == 0 && nocouponlist.length == 0\">\n\t\t\t</u-empty>\n\t\t\t<scroll-view scroll-y=\"true\" style=\"height: 832rpx;\" v-else>\n\t\t\t\t<view class=\"cou_item\" v-for=\"(item,index) in couponlist\" :key=\"index\">\n\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t<view class=\"box1\" v-if=\"item.type == 0\">\n\t\t\t\t\t\t\t<span>满</span>{{item.full}}<span>减</span>{{item.discount}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"box1\" v-else><span>￥</span>{{item.discount}}</view>\n\t\t\t\t\t\t<view class=\"box2\">\n\t\t\t\t\t\t\t<text>{{item.title}}</text>\n\t\t\t\t\t\t\t<span v-if=\"item.start_time == 0\">有效期：自领券日起{{item.day}}天</span>\n\t\t\t\t\t\t\t<span v-else>有效期：{{item.startTime}}</span>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"box3\" :style=\"item.choose?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t\t\t\t@click=\"chooseItemyh(item)\">\n\t\t\t\t\t\t\t<u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t{{item.rule}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"noYh\">\n\t\t\t\t\t<view class=\"left\">不可使用优惠券</view>\n\t\t\t\t\t<view class=\"right\" :style=\"notYh?'background:#2E80FE;border:2rpx solid #2E80FE':''\"\n\t\t\t\t\t\t@click=\"chooseNotyh()\"><u-icon name=\"checkbox-mark\" color=\"#fff\" size=\"16\"></u-icon></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"notcan\">\n\t\t\t\t\t不可使用优惠券\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cou_item\" style=\"border: 2rpx solid #ADADAD;background: #fff;\"\n\t\t\t\t\tv-for=\"(item,index) in nocouponlist\" :key=\"index\">\n\t\t\t\t\t<view class=\"top\" style=\"border-bottom: 2rpx dashed #ADADAD;\">\n\t\t\t\t\t\t<view class=\"box1\" v-if=\"item.type == 0\" style=\"color: #ADADAD ;\">\n\t\t\t\t\t\t\t<span>满</span>{{item.full}}<span>减</span>{{item.discount}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"box1\" style=\"color: #ADADAD ;\"><span>￥</span>{{item.discount}}</view>\n\t\t\t\t\t\t<view class=\"box2\">\n\t\t\t\t\t\t\t<view style=\"display: flex;  justify-content: space-between; align-items: center;\" class=\"\">\n\t\t\t\t\t\t\t\t<text style=\"color: #ADADAD ; \">{{item.title}}</text>\n\t\t\t\t\t\t\t\t<text v-if=\"item.available===0\" style=\"color: #E72427 ; font-size: 25rpx;\">{{item.availableText}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<span v-if=\"item.startTime == 0\">有效期：自领券日起{{item.day}}天</span>\n\t\t\t\t\t\t\t<span v-else>有效期：{{item.startTime}}</span>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t{{item.rule}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentIndex: 0,\n\t\t\t\tid: '',\n\t\t\t\tinfoyouhuij: '',\n\t\t\t\tprice: '',\n\t\t\t\tshowYh: false,\n\t\t\t\tcouponlist: [], //可用优惠券列表\n\t\t\t\tnocouponlist: [], //不可用优惠券列表，\n\t\t\t\tcouType: false, //是否选择优惠券\n\t\t\t\tconfirmCou: null, //当前选中的优惠券，\n\t\t\t\tcouponNum: '',\n\t\t\t\tgoods_id: '',\n\t\t\t\ttype: 0,\n\t\t\t\ttmplIds: 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY',\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tallprice() {\n\t\t\t\tconst discount = this.confirmCou == null ? 0 : this.confirmCou.discount * 1;\n\t\t\t\tconst result = this.price * 1 - discount;\n\t\t\t\treturn result <= 0 ? 0.01 : Number(result.toFixed(2));\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 检查当前平台\n\t\t\tgetCurrentPlatform() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\treturn 'app-plus';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\treturn 'mp-weixin';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn 'h5';\n\t\t\t\t// #endif\n\t\t\t\treturn 'unknown';\n\t\t\t},\n\t\t\tasync getList() {\n\t\t\t\tconsole.log(111)\n\t\t\t\tawait this.$api.service.myWelfare({\n\t\t\t\t\tstatus: 1,\n\t\t\t\t\tserviceId: this.goodsId,\n\t\t\t\t\tpayPrice: this.allprice\n\t\t\t\t}).then(res => {\n\t\t\t\t\tlet arr = []\n\t\t\t\t\tlet arr1 = []\n\t\t\t\t\tres.data.list.forEach(item => {\n\t\t\t\t\t\titem.choose = false\n\t\t\t\t\t\tif (item.available === 1) {\n\t\t\t\t\t\t\tarr.push(item)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tarr1.push(item)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tarr = [...new Set(arr)]\n\t\t\t\t\tarr1 = [...new Set(arr1)]\n\t\t\t\t\tthis.couponlist = arr\n\t\t\t\t\tthis.nocouponlist = arr1\n\t\t\t\t\tthis.couponNum = arr.length\n\t\t\t\t})\n\t\t\t},\n\t\t\tchooseNotyh() { //点击不适用优惠券\n\t\t\t\tthis.notYh = !this.notYh\n\t\t\t\tif (this.notYh) {\n\t\t\t\t\tthis.couponlist.forEach(item => {\n\t\t\t\t\t\titem.choose = false\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tchooseYh() { //打开优惠券弹框\n\t\t\t\tthis.showYh = true\n\t\t\t},\n\t\t\tchooseItemyh(item) {\n\t\t\t\tif (item.choose == false) {\n\t\t\t\t\t//判断当前是否符合使用条件\n\t\t\t\t\tif (item.type == 0) { //如果是满减券\n\t\t\t\t\t\tif (item.full * 1 > this.price * 1) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\ttitle: '当前金额未满足使用条件'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.couponlist.forEach(e => {\n\t\t\t\t\t\te.choose = false\n\t\t\t\t\t})\n\t\t\t\t\titem.choose = true //显示对勾\n\t\t\t\t\tthis.couType = true //当前已选优惠券\n\t\t\t\t\tthis.confirmCou = item\n\t\t\t\t} else {\n\t\t\t\t\titem.choose = false\n\t\t\t\t\tthis.couType = false\n\t\t\t\t\tthis.confirmCou = null\n\t\t\t\t}\n\t\t\t},\n\t\t\tconfirmPay() {\n\t\t\t\t// 获取当前平台\n\t\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\t\tconsole.log('当前平台:', platform);\n\n\t\t\t\tthis.$api.service.nowPay({\n\t\t\t\t\torderId: this.id,\n\t\t\t\t\tcouponId: this.confirmCou ? this.confirmCou.couponId : 0,\n\t\t\t\t\ttype: 1,\n\t\t\t\t\t// is_banlance: this.currentIndex\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif(res.code==='-1'){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}else{\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\tlet obj = res.data\n\t\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\n\t\t\t\t\t\tconsole.log(String(packageStr))\n\t\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\t\tconsole.log(packageStr)\n\t\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\t\tconsole.log(String(obj.timestamp))\n\t\t\t\t\t\tconsole.log(obj.sign)\n\n\t\t\t\t\t\t// 根据平台选择不同的支付方式\n\t\t\t\t\t\tif (platform === 'app-plus') {\n\t\t\t\t\t\t\t// APP环境使用微信支付\n\t\t\t\t\t\t\tconsole.log('APP环境，使用微信支付');\n\t\t\t\t\t\t\tthis.handleAppWechatPay(obj);\n\t\t\t\t\t\t} else if (platform === 'mp-weixin') {\n\t\t\t\t\t\t\t// 微信小程序环境保持原有逻辑\n\t\t\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\n\t\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 其他环境（H5等）\n\t\t\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\n\t\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// APP微信支付处理\n\t\t\thandleAppWechatPay(obj) {\n\t\t\t\tconsole.log(111)\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\"provider\": \"wxpay\",\n\t\t\t\t\t    orderInfo: 'orderInfo',\n\t\t\t\t\torderInfo: {\n\t\t\t\t\t\tappid: obj.appId,\n\t\t\t\t\t\tnoncestr: obj.nonceStr,\n\t\t\t\t\t\tpackage: 'Sign=WXPay',\n\t\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\t\tprepayid: obj.prepayId,\n\t\t\t\t\t\ttimestamp: String(obj.timestamp),\n\t\t\t\t\t\tsign: obj.sign\n\t\t\t\t\t},\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('APP微信支付成功', res);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\t// this.dingyue()\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\turl: '/user/order_list?tab=0'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('APP微信支付失败:', err);\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 微信小程序支付处理（保持原有逻辑）\n\t\t\thandleMiniProgramPay(obj) {\n\t\t\t\tconst paymentParams = {\n\t\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\n\t\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\t\tsignType: 'MD5',\n\t\t\t\t\tpaySign: obj.sign\n\t\t\t\t};\n\t\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\"provider\": 'wxpay',\n\t\t\t\t\ttimeStamp: String(obj.timestamp),\n\t\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\tsignType: \"MD5\",\n\t\t\t\t\tpaySign: obj.sign,\n\t\t\t\t\tappId: obj.appId,\n\t\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t\t// 支付成功回调\n\t\t\t\t\t\tconsole.log('支付成功', res1);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.dingyue()\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\turl: '/user/order_list?tab=0'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t// 支付失败回调\n\t\t\t\t\t\tconsole.error('requestPayment fail object:', err);\n\t\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n\t\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.error('支付失败', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败请检查网络',\n\t\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t})\n\t\t\t},\n\t\t\tdingyue() {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst anyAccepted = this.tmplIds.some(id => res[id] === 'accept');\n\t\t\t\t\t\tif (anyAccepted) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '消息订阅成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t\t// \ttitle: '订阅失败，请稍后重试',\n\t\t\t\t\t\t// \ticon: 'none'\n\t\t\t\t\t\t// });\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// async getDetail() {\n\t\t\t// \tconst res = await this.$api.service.orderdet({\n\t\t\t// \t\tid: this.id\n\t\t\t// \t})\n\t\t\t// \tthis.goods_id = res.order_goods[0].goods_id\n\t\t\t// }\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\tthis.infoyouhuij = this.$store.state.service.orderInfo\n\t\t\tconsole.log(this.infoyouhuij)\n\t\t\tconsole.log(options)\n\t\t\tthis.id = options.id\n\t\t\tthis.price = options.price\n\t\t\tthis.type = options.type\n\t\t\tthis.goodsId = options.goodsId\n\t\t\tthis.getList()\n\t\t\t// await this.getDetail()\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground-color: #f8f8f8;\n\t\theight: 100vh;\n\t\tpadding-top: 40rpx;\n\n\t\t.choose_yh {\n\t\t\tpadding-top: 40rpx;\n\t\t\twidth: 750rpx;\n\t\t\theight: 1106rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 20rpx 20rpx 0rpx 0rpx;\n\t\t\topacity: 1;\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\tz-index: 10088;\n\t\t\ttransition: all 0.5s;\n\n\t\t\t.head {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t\ttext-align: center;\n\t\t\t\tmargin-bottom: 44rpx;\n\t\t\t}\n\n\t\t\t.close {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 44rpx;\n\t\t\t\tright: 32rpx;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 37rpx;\n\t\t\t\t\theight: 37rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.cou_item {\n\t\t\t\tmargin: 0 auto;\n\t\t\t\twidth: 690rpx;\n\t\t\t\theight: 202rpx;\n\t\t\t\tbackground: #DCEAFF;\n\t\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tborder: 2rpx solid #2E80FE;\n\n\t\t\t\t.top {\n\t\t\t\t\theight: 150rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tpadding-top: 26rpx;\n\t\t\t\t\tpadding-left: 24rpx;\n\t\t\t\t\tpadding-right: 14rpx;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tborder-bottom: 2rpx dashed #2E80FE;\n\n\t\t\t\t\t.box1 {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\twidth: 185rpx;\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #E72427;\n\n\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\tfont-size: 15rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.box2 {\n\t\t\t\t\t\tmargin-left: 28rpx;\n\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tcolor: #171717;\n\t\t\t\t\t\t\tmax-width: 450rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.box3 {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tright: 22rpx;\n\t\t\t\t\t\ttop: 40rpx;\n\t\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\tbackground: #fff;\n\t\t\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.bottom {\n\t\t\t\t\tpadding: 0 24rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.noYh {\n\t\t\t\twidth: 690rpx;\n\t\t\t\tmargin: 0 auto;\n\t\t\t\tmargin-top: 52rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 0 22rpx;\n\n\t\t\t\t.left {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t}\n\n\t\t\t\t.right {\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tbackground: #fff;\n\t\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.notcan {\n\t\t\t\tmargin-top: 52rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #B2B2B2;\n\t\t\t\tpadding: 0 30rpx;\n\t\t\t}\n\t\t}\n\n\t\t.time {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\n\t\t\ttext {\n\t\t\t\tmargin-right: 15rpx;\n\t\t\t}\n\t\t}\n\n\t\t.price {\n\t\t\tdisplay: inline-block;\n\t\t\twidth: 750rpx;\n\t\t\ttext-align: center;\n\t\t\tmargin-top: 20rpx;\n\t\t\tfont-size: 80rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #292C39;\n\n\t\t\tspan {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t}\n\t\t}\n\n\t\t.payCard {\n\t\t\tmargin: 0 auto;\n\t\t\twidth: 686rpx;\n\t\t\theight: 130rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\n\t\t\tmargin-top: 40rpx;\n\t\t\tpadding: 0 20rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.left {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 70rpx;\n\t\t\t\t\theight: 70rpx;\n\t\t\t\t}\n\n\t\t\t\ttext {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #E72427;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.choose {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tborder: 2rpx solid #ADADAD;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\n\t\t.footer {\n\t\t\twidth: 750rpx;\n\t\t\theight: 192rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\t// background-color: #FFFFFF;\n\n\t\t\t.btn {\n\t\t\t\twidth: 686rpx;\n\t\t\t\theight: 88rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 44rpx 44rpx 44rpx 44rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tline-height: 88rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Cashier.vue?vue&type=style&index=0&id=5b297b6d&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Cashier.vue?vue&type=style&index=0&id=5b297b6d&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756176210064\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}