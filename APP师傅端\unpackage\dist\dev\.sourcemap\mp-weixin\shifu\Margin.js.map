{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?19d3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?84fc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?5629", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?06ac", "uni-app:///shifu/Margin.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?bcd7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?4fb3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "cashPledge", "cashPledgeFreeze", "refundableAmount", "reason", "config<PERSON>ash<PERSON>ledge", "methods", "getCurrentPlatform", "handleAppWechatPay", "console", "uni", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "title", "icon", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "confirmRefund", "content", "cancelText", "confirmText", "tui<PERSON><PERSON>", "getMoney", "res", "getprice", "submit", "confirmPay", "payPrice", "couponId", "type", "onLoad", "refreshData", "Promise", "onPullDownRefresh", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6Bz2B;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MAKA;MAKA;IACA;IACA;IACAC;MAAA;QAAA;MACAC;MACAA;MACAC;QACA;QACAC;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEAEA;QACAT;QACAC;UACAS;UACAC;QACA;QACA;QACA;MACA,+DACA;QACAX;QACA;UACAC;YACAS;YACAC;UACA;QACA;UACAV;YACAS;YACAC;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAC;MAAA;MACA;QACAC;QAAA;QACAC;QACAT;QACAU;QACAC;MACA;MACAhB;MACAC;QACA;QACAY;QACAC;QACAT;QACAC;QACAS;QACAC;QACAC;QACAC;UACA;UACAlB;UACAC;YACAS;YACAC;UACA;UACA;UACA;QACA;QACAQ;UACA;UACAnB;UACAA;UACA;YACAC;cACAS;cACAC;YACA;UACA;YACAV;cACAS;cACAC;YACA;UACA;UACAX;UACAC;YACAS;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAS;MAAA;MACAnB;QACAS;QACAW;QACAC;QACAC;QACAL;UACA;YACA;YACA;UACA;YACA;YACAlB;UACA;QACA;MACA;IACA;IAEA;IACAwB;MAAA;MACA;QACA;UACAvB;YACAU;YACAD;UACA;QACA;UACAT;YACAU;YACAD;UACA;UACA;UACA;QACA;MACA;IACA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1B;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAD;gBACA;kBACA1B;gBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA4B;MACA;QACA;MACA;QACA3B;UACAU;UACAD;QACA;MACA;IACA;IACAmB;MAAA;MACA;MACA7B;MACA;QACA8B;QACAC;QACAC;MACA;QACA;UACA/B;YACAS;YACAC;UACA;QACA;UACAX;UACA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UAMA;YACAa;YAAA;YACAC;YACAT;YACAU;YACAC;UACA;UACAhB;UACAA;UACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;QAEA;MACA;IACA;EACA;EACAiC;IACA;IACA;EACA;EAEA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAGAC,aACA,mBACA,kBACA;YAAA;cACAnC;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAoC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cACAnC;gBACAS;gBACAC;gBACA0B;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEArC;cACAC;gBACAS;gBACAC;cACA;YAAA;cAAA;cAEAV;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACjVA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/Margin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/Margin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Margin.vue?vue&type=template&id=92a41b30&scoped=true&\"\nvar renderjs\nimport script from \"./Margin.vue?vue&type=script&lang=js&\"\nexport * from \"./Margin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92a41b30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/Margin.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=template&id=92a41b30&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"box\">\n\t\t\t<view class=\"money\">{{ refundableAmount }}</view>\n\t\t\t<view class=\"title\">保证金金额（元）</view>\n\n\t\t\t<view v-if=\"cashPledgeFreeze > 0\" class=\"freeze-info\">\n\t\t\t\t<view class=\"freeze-title\">保证金扣除信息</view>\n\t\t\t\t<view class=\"freeze-item\">\n\t\t\t\t\t<text class=\"freeze-label\">已交金额：</text>\n\t\t\t\t\t<text class=\"freeze-value\">{{ cashPledge }}元</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"freeze-item\">\n\t\t\t\t\t<text class=\"freeze-label\">扣除金额：</text>\n\t\t\t\t\t<text class=\"freeze-value freeze-amount\">{{ cashPledgeFreeze }}元</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"reason\" class=\"freeze-item\">\n\t\t\t\t\t<text class=\"freeze-label\">扣除原因：</text>\n\t\t\t\t\t<text class=\"freeze-value\">{{ reason }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"btn\" @click=\"submit\">缴纳保证金</view>\n\t\t\t<view v-if=\"money > 0\" class=\"btn\" @click=\"confirmRefund\">退还保证金</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tmoney: '',\n\t\t\tcashPledge: '', // 交的钱\n\t\t\tcashPledgeFreeze: '', // 冻结的\n\t\t\trefundableAmount: '', // 可退金额\n\t\t\treason: '', // 冻结的原因\n\t\t\tconfigCashPledge: '', // 配置中的保证金金额\n\t\t}\n\t},\n\tmethods: {\n\t\tgetCurrentPlatform() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\treturn 'app-plus';\n\t\t\t// #endif\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\treturn 'mp-weixin';\n\t\t\t// #endif\n\t\t\t// #ifdef H5\n\t\t\treturn 'h5';\n\t\t\t// #endif\n\t\t\treturn 'unknown';\n\t\t},\n\t\t// APP微信支付处理\n\t\thandleAppWechatPay(obj) {\n\t\t\tconsole.log(111)\n\t\t\tconsole.log(obj)\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": \"wxpay\",\n\t\t\t\torderInfo: 'orderInfo',\n\t\t\t\torderInfo: {\n\t\t\t\t\tappid: obj.appId,\n\t\t\t\t\tnoncestr: obj.nonceStr,\n\t\t\t\t\tpackage: 'Sign=WXPay',\n\t\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\t\tprepayid: obj.prepayId,\n\t\t\t\t\ttimestamp: String(obj.timestamp),\n\t\t\t\t\tsign: obj.sign\n\t\t\t\t},\n\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('APP微信支付成功', res);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\t// 支付成功后刷新数据\n\t\t\t\t\tthis.refreshData();\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('APP微信支付失败:', err);\n\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 微信小程序支付处理（保持原有逻辑）\n\t\thandleMiniProgramPay(obj) {\n\t\t\tconst paymentParams = {\n\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tsignType: 'MD5',\n\t\t\t\tpaySign: obj.sign\n\t\t\t};\n\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\tuni.requestPayment({\n\t\t\t\t\"provider\": 'wxpay',\n\t\t\t\ttimeStamp: String(obj.timestamp),\n\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\tpartnerid: obj.partnerId,\n\t\t\t\tsignType: \"MD5\",\n\t\t\t\tpaySign: obj.sign,\n\t\t\t\tappId: obj.appId,\n\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t// 支付成功回调\n\t\t\t\t\tconsole.log('支付成功', res1);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\t// 支付成功后刷新数据\n\t\t\t\t\tthis.refreshData();\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\t// 支付失败回调\n\t\t\t\t\tconsole.error('requestPayment fail object:', err);\n\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\n\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '您已取消支付',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tconsole.error('支付失败', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付失败请检查网络',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t})\n\t\t},\n\n\t\t// 新增的确认退款方法\n\t\tconfirmRefund() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认退款',\n\t\t\t\tcontent: '确定要申请退还保证金吗？',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tconfirmText: '确认',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 用户点击了确认，执行退款\n\t\t\t\t\t\tthis.tuikuan();\n\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t// 用户点击了取消，不执行任何操作\n\t\t\t\t\t\tconsole.log('用户取消了退款');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 保持原有的退款方法\n\t\ttuikuan() {\n\t\t\tthis.$api.shifu.tuikuanBzj().then(res => {\n\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t},5000)\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.data\n\t\t\t\t\t},5000)\n\t\t\t\t\t// 退款成功后刷新数据\n\t\t\t\t\tthis.refreshData()\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tasync getMoney() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.seeBzj();\n\t\t\t\tif (res === -1) {\n\t\t\t\t\tthis.money = 0;\n\t\t\t\t\tthis.cashPledge = 0;\n\t\t\t\t\tthis.cashPledgeFreeze = 0;\n\t\t\t\t\tthis.refundableAmount = 0;\n\t\t\t\t\tthis.reason = '';\n\t\t\t\t} else if (res.code === '200' && res.data) {\n\t\t\t\t\t// 根据新的数据结构设置各个字段\n\t\t\t\t\tthis.cashPledge = res.data.cashPledge || 0; // 交的钱\n\t\t\t\t\tthis.cashPledgeFreeze = res.data.cashPledgeFreeze || 0; // 冻结的\n\t\t\t\t\tthis.refundableAmount = res.data.refundableAmount || 0; // 可退金额\n\t\t\t\t\tthis.reason = res.data.reason || ''; // 冻结的原因\n\n\t\t\t\t\t// money 显示已交的保证金金额\n\t\t\t\t\tthis.money = this.cashPledge;\n\t\t\t\t} else {\n\t\t\t\t\tthis.money = 0;\n\t\t\t\t\tthis.cashPledge = 0;\n\t\t\t\t\tthis.cashPledgeFreeze = 0;\n\t\t\t\t\tthis.refundableAmount = 0;\n\t\t\t\t\tthis.reason = '';\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取保证金失败:', error);\n\t\t\t\tthis.money = 0;\n\t\t\t\tthis.cashPledge = 0;\n\t\t\t\tthis.cashPledgeFreeze = 0;\n\t\t\t\tthis.refundableAmount = 0;\n\t\t\t\tthis.reason = '';\n\t\t\t}\n\t\t},\n\t\tasync getprice() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.base.getConfig();\n\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\tconsole.error('获取配置失败:', res.msg);\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(res);\n\t\t\t\t\tthis.configCashPledge = res.data.cashPledge;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取价格配置失败:', error);\n\t\t\t}\n\t\t},\n\t\tsubmit() {\n\t\t\tif (this.money == 0 || this.money == '') {\n\t\t\t\tthis.confirmPay()\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '您已缴纳保证金快去接单吧'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tconfirmPay() {\n\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\tconsole.log('当前平台:', platform);\n\t\t\tthis.$api.shifu.nowPay({\n\t\t\t\tpayPrice: this.configCashPledge,\n\t\t\t\tcouponId: 0,\n\t\t\t\ttype: 1,\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tlet obj = res.data\n\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\n\t\t\t\t\tconsole.log(String(packageStr))\n\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\tconsole.log(packageStr)\n\t\t\t\t\tconsole.log(obj.nonceStr)\n\t\t\t\t\tconsole.log(String(obj.timestamp))\n\t\t\t\t\tconsole.log(obj.sign)\n\n\n\n\n\n\t\t\t\t\tconst paymentParams = {\n\t\t\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string \n\t\t\t\t\t\tnonceStr: obj.nonceStr,\n\t\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\n\t\t\t\t\t\tsignType: 'MD5',\n\t\t\t\t\t\tpaySign: obj.sign\n\t\t\t\t\t};\n\t\t\t\t\tconsole.log(obj)\n\t\t\t\t\tconsole.log(JSON.stringify(paymentParams));\n\t\t\t\t\t// 根据平台选择不同的支付方式\n\t\t\t\t\tif (platform === 'app-plus') {\n\t\t\t\t\t\t// APP环境使用微信支付\n\t\t\t\t\t\tconsole.log('APP环境，使用微信支付');\n\t\t\t\t\t\tthis.handleAppWechatPay(obj);\n\t\t\t\t\t} else if (platform === 'mp-weixin') {\n\t\t\t\t\t\t// 微信小程序环境保持原有逻辑\n\t\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\n\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 其他环境（H5等）\n\t\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\n\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.getMoney()\n\t\tthis.getprice()\n\t},\n\n\t// 刷新数据方法\n\tasync refreshData() {\n\t\ttry {\n\t\t\t// 重新获取保证金信息和价格配置\n\t\t\tawait Promise.all([\n\t\t\t\tthis.getMoney(),\n\t\t\t\tthis.getprice()\n\t\t\t]);\n\t\t\tconsole.log('数据刷新成功');\n\t\t} catch (error) {\n\t\t\tconsole.error('数据刷新失败:', error);\n\t\t}\n\t},\n\n\t// 下拉刷新\n\tasync onPullDownRefresh() {\n\t\ttry {\n\t\t\tawait this.refreshData();\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '刷新成功',\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: 1000\n\t\t\t});\n\t\t} catch (error) {\n\t\t\tconsole.error('下拉刷新失败:', error);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '刷新失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t} finally {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground: #F8F8F8;\n\theight: 100vh;\n\n\t.box {\n\t\tpadding: 50rpx 82rpx;\n\t\tbackground: #fff;\n\n\t\t.money {\n\t\t\tmargin: 0 auto;\n\t\t\twidth: fit-content;\n\t\t\tfont-size: 80rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #171717;\n\t\t}\n\n\t\t.title {\n\t\t\tmargin: 0 auto;\n\t\t\tmargin-top: 20rpx;\n\t\t\twidth: fit-content;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #171717;\n\t\t}\n\n\t\t.freeze-info {\n\t\t\tmargin-top: 40rpx;\n\t\t\tpadding: 30rpx;\n\t\t\tbackground: #FFF9E6;\n\t\t\tborder-radius: 12rpx;\n\t\t\tborder-left: 4rpx solid #FF9500;\n\n\t\t\t.freeze-title {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #FF9500;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\n\t\t\t.freeze-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\t.freeze-label {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #666666;\n\t\t\t\t}\n\n\t\t\t\t.freeze-value {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tfont-weight: 500;\n\n\t\t\t\t\t&.freeze-amount {\n\t\t\t\t\t\tcolor: #FF4444;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.btn {\n\t\t\tmargin: 0 auto;\n\t\t\tmargin-top: 64rpx;\n\t\t\twidth: 584rpx;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756176202932\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}