{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-rate/u-rate.vue?c77e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-rate/u-rate.vue?5b59", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-rate/u-rate.vue?bdec", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-rate/u-rate.vue?06c6", "uni-app:///node_modules/uview-ui/components/u-rate/u-rate.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-rate/u-rate.vue?90b1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-rate/u-rate.vue?5c2f"], "names": ["name", "mixins", "data", "elId", "elClass", "rateBoxLeft", "activeIndex", "rateWidth", "moving", "watch", "value", "methods", "init", "uni", "getRateItemRect", "getRateIconWrapRect", "touchMove", "touchEnd", "clickHandler", "x", "emitEvent", "getActiveIndex", "index", "setTimeout", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8Ez2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,eAqBA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAJ;EACA;EACAK;IACAC;MAAA;MACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAD;cAAA;gBACA;;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAOA;IACA;IACAE;MAAA;MACA;;MAEA;QACA;MACA;IAUA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;MACA;;MAEAC;MAMA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;QACA;MACA;MACA;MACA;MACA;MACAF;MACA;MACA;MACA;MACA;MACA;MACA;QACAG;QACA;QACA;QACA;UACAA;QACA;UACAA;QACA;MACA;QACAA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MAEA;MACA;MACA;MACA;QACA;MACA;;MAEA;MACAC;QACA;MACA;MACA;MACAA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvQA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-rate/u-rate.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-rate.vue?vue&type=template&id=01de4127&scoped=true&\"\nvar renderjs\nimport script from \"./u-rate.vue?vue&type=script&lang=js&\"\nexport * from \"./u-rate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-rate.vue?vue&type=style&index=0&id=01de4127&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"01de4127\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-rate/u-rate.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-rate.vue?vue&type=template&id=01de4127&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  var g0 = Math.floor(_vm.activeIndex)\n  var g1 = !_vm.disabled ? Math.floor(_vm.activeIndex) : null\n  var g2 = _vm.allowHalf ? _vm.$u.addUnit(_vm.rateWidth / 2) : null\n  var g3 = _vm.allowHalf ? Math.ceil(_vm.activeIndex) : null\n  var g4 = _vm.allowHalf && !_vm.disabled ? Math.ceil(_vm.activeIndex) : null\n  var l0 = _vm.__map(Number(_vm.count), function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var a0 = {\n      \"padding-left\": _vm.$u.addUnit(_vm.gutter / 2),\n      \"padding-right\": _vm.$u.addUnit(_vm.gutter / 2),\n    }\n    var a1 = _vm.allowHalf\n      ? {\n          \"padding-left\": _vm.$u.addUnit(_vm.gutter / 2),\n          \"padding-right\": _vm.$u.addUnit(_vm.gutter / 2),\n        }\n      : null\n    return {\n      $orig: $orig,\n      a0: a0,\n      a1: a1,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      $event.stopPropagation()\n      return _vm.clickHandler($event, index + 1)\n    }\n    _vm.e1 = function ($event, index) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        index = _temp4.index\n      var _temp3, _temp4\n      $event.stopPropagation()\n      return _vm.clickHandler($event, index + 1)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-rate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-rate.vue?vue&type=script&lang=js&\"", "<template>\n    <view\n        class=\"u-rate\"\n        :id=\"elId\"\n        ref=\"u-rate\"\n        :style=\"[$u.addStyle(customStyle)]\"\n    >\n        <view\n            class=\"u-rate__content\"\n            @touchmove.stop=\"touchMove\"\n            @touchend.stop=\"touchEnd\"\n        >\n            <view\n                class=\"u-rate__content__item\"\n                v-for=\"(item, index) in Number(count)\"\n                :key=\"index\"\n                :class=\"[elClass]\"\n            >\n                <view\n                    class=\"u-rate__content__item__icon-wrap\"\n                    ref=\"u-rate__content__item__icon-wrap\"\n                    @tap.stop=\"clickHandler($event, index + 1)\"\n                >\n                    <u-icon\n                        :name=\"\n                            Math.floor(activeIndex) > index\n                                ? activeIcon\n                                : inactiveIcon\n                        \"\n                        :color=\"\n                            disabled\n                                ? '#c8c9cc'\n                                : Math.floor(activeIndex) > index\n                                ? activeColor\n                                : inactiveColor\n                        \"\n                        :custom-style=\"{\n                            'padding-left': $u.addUnit(gutter / 2),\n\t\t\t\t\t\t\t'padding-right': $u.addUnit(gutter / 2)\n                        }\"\n                        :size=\"size\"\n                    ></u-icon>\n                </view>\n                <view\n                    v-if=\"allowHalf\"\n                    @tap.stop=\"clickHandler($event, index + 1)\"\n                    class=\"u-rate__content__item__icon-wrap u-rate__content__item__icon-wrap--half\"\n                    :style=\"[{\n                        width: $u.addUnit(rateWidth / 2),\n                    }]\"\n                    ref=\"u-rate__content__item__icon-wrap\"\n                >\n                    <u-icon\n                        :name=\"\n                            Math.ceil(activeIndex) > index\n                                ? activeIcon\n                                : inactiveIcon\n                        \"\n                        :color=\"\n                            disabled\n                                ? '#c8c9cc'\n                                : Math.ceil(activeIndex) > index\n                                ? activeColor\n                                : inactiveColor\n                        \"\n                        :custom-style=\"{\n\t\t\t\t\t\t\t'padding-left': $u.addUnit(gutter / 2),\n\t\t\t\t\t\t\t'padding-right': $u.addUnit(gutter / 2)\n                        }\"\n                        :size=\"size\"\n                    ></u-icon>\n                </view>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\n\timport props from './props.js';\n\n\t// #ifdef APP-NVUE\n\tconst dom = weex.requireModule(\"dom\");\n\t// #endif\n\t/**\n\t * rate 评分\n\t * @description 该组件一般用于满意度调查，星型评分的场景\n\t * @tutorial https://www.uviewui.com/components/rate.html\n\t * @property {String | Number}\tvalue\t\t\t用于v-model双向绑定选中的星星数量 (默认 1 )\n\t * @property {String | Number}\tcount\t\t\t最多可选的星星数量 （默认 5 ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁止用户操作 （默认 false ）\n\t * @property {Boolean}\t\t\treadonly\t\t是否只读 （默认 false ）\n\t * @property {String | Number}\tsize\t\t\t星星的大小，单位px （默认 18 ）\n\t * @property {String}\t\t\tinactiveColor\t未选中星星的颜色 （默认 '#b2b2b2' ）\n\t * @property {String}\t\t\tactiveColor\t\t选中的星星颜色 （默认 '#FA3534' ）\n\t * @property {String | Number}\tgutter\t\t\t星星之间的距离 （默认 4 ）\n\t * @property {String | Number}\tminCount\t\t最少选中星星的个数 （默认 1 ）\n\t * @property {Boolean}\t\t\tallowHalf\t\t是否允许半星选择 （默认 false ）\n\t * @property {String}\t\t\tactiveIcon\t\t选中时的图标名，只能为uView的内置图标 （默认 'star-fill' ）\n\t * @property {String}\t\t\tinactiveIcon\t未选中时的图标名，只能为uView的内置图标 （默认 'star' ）\n\t * @property {Boolean}\t\t\ttouchable\t\t是否可以通过滑动手势选择评分 （默认 'true' ）\n\t * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\n\t * @event {Function} change 选中的星星发生变化时触发\n\t * @example <u-rate :count=\"count\" :value=\"2\"></u-rate>\n\t */\n\texport default {\n\t\tname: \"u-rate\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 生成一个唯一id，否则一个页面多个评分组件，会造成冲突\n\t\t\t\telId: uni.$u.guid(),\n\t\t\t\telClass: uni.$u.guid(),\n\t\t\t\trateBoxLeft: 0, // 评分盒子左边到屏幕左边的距离，用于滑动选择时计算距离\n\t\t\t\tactiveIndex: this.value,\n\t\t\t\trateWidth: 0, // 每个星星的宽度\n\t\t\t\t// 标识是否正在滑动，由于iOS事件上touch比click先触发，导致快速滑动结束后，接着触发click，导致事件混乱而出错\n\t\t\t\tmoving: false,\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\tvalue(val) {\n\t\t\t\tthis.activeIndex = val;\n\t\t\t},\n\t\t\tactiveIndex: 'emitEvent'\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tuni.$u.sleep().then(() => {\n\t\t\t\t\tthis.getRateItemRect();\n\t\t\t\t\tthis.getRateIconWrapRect();\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取评分组件盒子的布局信息\n\t\t\tasync getRateItemRect() {\n\t\t\t\tawait uni.$u.sleep();\n\t\t\t\t// uView封装的获取节点的方法，详见文档\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$uGetRect(\"#\" + this.elId).then((res) => {\n\t\t\t\t\tthis.rateBoxLeft = res.left;\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(this.$refs[\"u-rate\"], (res) => {\n\t\t\t\t\tthis.rateBoxLeft = res.size.left;\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 获取单个星星的尺寸\n\t\t\tgetRateIconWrapRect() {\n\t\t\t\t// uView封装的获取节点的方法，详见文档\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$uGetRect(\".\" + this.elClass).then((res) => {\n\t\t\t\t\tthis.rateWidth = res.width;\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(\n\t\t\t\t\tthis.$refs[\"u-rate__content__item__icon-wrap\"][0],\n\t\t\t\t\t(res) => {\n\t\t\t\t\t\tthis.rateWidth = res.size.width;\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 手指滑动\n\t\t\ttouchMove(e) {\n\t\t\t\t// 如果禁止通过手动滑动选择，返回\n\t\t\t\tif (!this.touchable) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.preventEvent(e);\n\t\t\t\tconst x = e.changedTouches[0].pageX;\n\t\t\t\tthis.getActiveIndex(x);\n\t\t\t},\n\t\t\t// 停止滑动\n\t\t\ttouchEnd(e) {\n\t\t\t\t// 如果禁止通过手动滑动选择，返回\n\t\t\t\tif (!this.touchable) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.preventEvent(e);\n\t\t\t\tconst x = e.changedTouches[0].pageX;\n\t\t\t\tthis.getActiveIndex(x);\n\t\t\t},\n\t\t\t// 通过点击，直接选中\n\t\t\tclickHandler(e, index) {\n\t\t\t\t// ios上，moving状态取消事件触发\n\t\t\t\tif (uni.$u.os() === \"ios\" && this.moving) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.preventEvent(e);\n\t\t\t\tlet x = 0;\n\t\t\t\t// 点击时，在nvue上，无法获得点击的坐标，所以无法实现点击半星选择\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tx = e.changedTouches[0].pageX;\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// nvue下，无法通过点击获得坐标信息，这里通过元素的位置尺寸值模拟坐标\n\t\t\t\tx = index * this.rateWidth + this.rateBoxLeft;\n\t\t\t\t// #endif\n\t\t\t\tthis.getActiveIndex(x,true);\n\t\t\t},\n\t\t\t// 发出事件\n\t\t\temitEvent() {\n\t\t\t\t// 发出change事件\n\t\t\t\tthis.$emit(\"change\", this.activeIndex);\n\t\t\t\t// 同时修改双向绑定的value的值\n\t\t\t\tthis.$emit(\"input\", this.activeIndex);\n\t\t\t},\n\t\t\t// 获取当前激活的评分图标\n\t\t\tgetActiveIndex(x,isClick = false) {\n\t\t\t\tif (this.disabled || this.readonly) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// 判断当前操作的点的x坐标值，是否在允许的边界范围内\n\t\t\t\tconst allRateWidth = this.rateWidth * this.count + this.rateBoxLeft;\n\t\t\t\t// 如果小于第一个图标的左边界，设置为最小值，如果大于所有图标的宽度，则设置为最大值\n\t\t\t\tx = uni.$u.range(this.rateBoxLeft, allRateWidth, x) - this.rateBoxLeft\n\t\t\t\t// 滑动点相对于评分盒子左边的距离\n\t\t\t\tconst distance = x;\n\t\t\t\t// 滑动的距离，相当于多少颗星星\n\t\t\t\tlet index;\n\t\t\t\t// 判断是否允许半星\n\t\t\t\tif (this.allowHalf) {\n\t\t\t\t\tindex = Math.floor(distance / this.rateWidth);\n\t\t\t\t\t// 取余，判断小数的区间范围\n\t\t\t\t\tconst decimal = distance % this.rateWidth;\n\t\t\t\t\tif (decimal <= this.rateWidth / 2 && decimal > 0) {\n\t\t\t\t\t\tindex += 0.5;\n\t\t\t\t\t} else if (decimal > this.rateWidth / 2) {\n\t\t\t\t\t\tindex++;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tindex = Math.floor(distance / this.rateWidth);\n\t\t\t\t\t// 取余，判断小数的区间范围\n\t\t\t\t\tconst decimal = distance % this.rateWidth;\n\t\t\t\t\t// 非半星时，只有超过了图标的一半距离，才认为是选择了这颗星\n\t\t\t\t\tif (isClick){\n\t\t\t\t\t\tif (decimal > 0) index++;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (decimal > this.rateWidth / 2) index++;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t\tthis.activeIndex = Math.min(index, this.count);\n\t\t\t\t// 对最少颗星星的限制\n\t\t\t\tif (this.activeIndex < this.minCount) {\n\t\t\t\t\tthis.activeIndex = this.minCount;\n\t\t\t\t}\n\n\t\t\t\t// 设置延时为了让click事件在touchmove之前触发\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.moving = true;\n\t\t\t\t}, 10);\n\t\t\t\t// 一定时间后，取消标识为移动中状态，是为了让click事件无效\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.moving = false;\n\t\t\t\t}, 10);\n\t\t\t},\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init();\n\t\t},\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n$u-rate-margin: 0 !default;\n$u-rate-padding: 0 !default;\n$u-rate-item-icon-wrap-half-top: 0 !default;\n$u-rate-item-icon-wrap-half-left: 0 !default;\n\n.u-rate {\n    @include flex;\n    align-items: center;\n    margin: $u-rate-margin;\n    padding: $u-rate-padding;\n    /* #ifndef APP-NVUE */\n    touch-action: none;\n    /* #endif */\n\n    &__content {\n        @include flex;\n\n\t\t&__item {\n\t\t    position: relative;\n\n\t\t    &__icon-wrap {\n\t\t        &--half {\n\t\t            position: absolute;\n\t\t            overflow: hidden;\n\t\t            top: $u-rate-item-icon-wrap-half-top;\n\t\t            left: $u-rate-item-icon-wrap-half-left;\n\t\t        }\n\t\t    }\n\t\t}\n    }\n}\n\n.u-icon {\n    /* #ifndef APP-NVUE */\n    box-sizing: border-box;\n    /* #endif */\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-rate.vue?vue&type=style&index=0&id=01de4127&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-rate.vue?vue&type=style&index=0&id=01de4127&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756169663283\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}