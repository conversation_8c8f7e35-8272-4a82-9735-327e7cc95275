<template>
	<view class="page">
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity" v-if="flag"></u-picker>
		<view class="top">请选择你所在的城市以获取周边服务</view>
		<view class="main">
			<view class="main_item location-item">
				<view class="location-left">
					<view class="location-icon">📍</view>
					<view class="location-text">当前定位</view>
				</view>
				<view class="location-center">
					<view class="address-text">{{ form.address }}</view>
				</view>
				<view class="location-right" @tap="relocate">
					<view class="relocate-btn">重新定位</view>
				</view>
			</view>

			<view class="main_item">
				<view class="name">所在区域</view>
				<input type="text" v-model="form.city" placeholder="请选择所在区域" disabled @click="showCity = true">
			</view>
		</view>
		<view class="btn" @click="confirmSelection">确定</view>
	</view>
</template>

<script>
import locationManager from '@/utils/location-manager.js'

export default {
	data() {
		return {
			flag: false,
			loading: false,
			showCity: false,
			form: {
				address: '点击选择服务地址',
				city: '',
				cityId: '',
				lng: '',
				lat: ''
			},
			selectedProvince: '',
			selectedCity: '',
			selectedDistrict: '',
			cityDetailInfo: null, // 保存API返回的完整城市信息
			columnsCity: [
				[], // Province
				[], // City
				[]  // Area
			],
		}
	},
	onLoad() {
		this.getCity(0)
		this.loadSavedLocationInfo()
		this.getNowPosition()
	},
	methods: {
		// 加载已保存的定位信息
		loadSavedLocationInfo() {
			try {
				// 优先从userSelectedCity获取
				const savedCity = uni.getStorageSync('userSelectedCity');
				if (savedCity && savedCity.address) {
					this.form.address = savedCity.address;
					this.form.lng = savedCity.lng || '';
					this.form.lat = savedCity.lat || '';
					console.log('加载已保存的城市信息:', savedCity);
					return;
				}

				// 兼容旧的存储格式
				const lng = uni.getStorageSync('lng');
				const lat = uni.getStorageSync('lat');
				const regeocode = uni.getStorageSync('regeocode');

				if (lng && lat && regeocode && regeocode.formatted_address) {
					this.form.address = regeocode.formatted_address;
					this.form.lng = lng;
					this.form.lat = lat;
					console.log('加载旧格式的定位信息');
				}
			} catch (error) {
				console.error('加载已保存的定位信息失败:', error);
			}
		},

		getNowPosition() {
			return new Promise((resolve) => {
				uni.getLocation({
					type: "gcj02",
					isHighAccuracy: true,
					accuracy: "best",
					success: (res) => {
						uni.setStorageSync("lat", res.latitude);
						uni.setStorageSync("lng", res.longitude);
						uni.request({
							url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
							success: (res1) => {
								console.log(res1)
								this.form.address = res1.data.regeocode.formatted_address
								// Store coordinates
								this.form.lng = res.longitude;
								this.form.lat = res.latitude;
								resolve();
							},
							fail: (err) => {
								console.error("逆地理编码失败:", err);
								resolve();
							}
						});
					},
					fail: (err) => {
						console.error("获取定位失败:", err);
						resolve();
					}
				});
			});
		},

		// 重新定位方法 - 使用统一的location-manager
		async relocate() {
			try {
				// 使用location-manager强制更新定位
				const locationData = await locationManager.getLocation({
					forceUpdate: true,
					silent: false
				});

				if (locationData) {
					// 更新页面显示
					this.form.address = locationData.address || '定位成功';
					this.form.lng = locationData.lng;
					this.form.lat = locationData.lat;

					// 构造完整的定位信息，确保包含所有必要字段
					const locationInfo = {
						city: locationData.district || locationData.city || '未知位置',
						cityId: locationData.adcode || '',
						cityName: locationData.city || '',
						address: locationData.address || '定位成功',
						lng: locationData.lng || '',
						lat: locationData.lat || '',
						province: locationData.province || '',
						district: locationData.district || '',
						fullPath: `${locationData.province || ''},${locationData.city || ''},${locationData.district || ''}`,
						detail: {
							id: locationData.adcode || '',
							title: locationData.district || locationData.city || '',
							trueName: locationData.district || locationData.city || '',
							mergename: `中国,${locationData.province || ''},${locationData.city || ''},${locationData.district || ''}`,
							level: 3
						},
						type: 'location', // 标记为定位获取
						timestamp: Date.now()
					};

					// 使用LocationManager的方法保存数据，确保lng、lat、locationData都被正确存储
					locationManager.saveUserSelectedCity(locationInfo);

					uni.showToast({
						title: '定位成功',
						icon: 'success',
						duration: 1500
					});

					console.log('重新定位成功:', locationData);
				}
			} catch (error) {
				console.error('重新定位失败:', error);

				// 如果location-manager失败，显示权限设置弹窗
				// #ifdef APP-PLUS
				uni.showModal({
					title: '定位权限',
					content: '获取定位失败，是否前往设置开启定位权限？',
					confirmText: '去设置',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							locationManager.showLocationPermissionModal();
						}
					}
				});
				// #endif

				// #ifdef MP-WEIXIN
				locationManager.showLocationPermissionModal();
				// #endif
			}
		},

		goMap() {
			let that = this
			// #ifdef MP-WEIXIN
			uni.authorize({
				scope: 'scope.userLocation',
				success(res) {
					setTimeout(() => {
						uni.chooseLocation({
							success: function (res) {
								console.log('选择位置成功:', res);
								try {
									that.form.address = res.name || '未知位置'
									that.form.lng = res.longitude || ''
									that.form.lat = res.latitude || ''

									uni.showToast({
										title: '位置选择成功',
										icon: 'success',
										duration: 1500
									});
								} catch (error) {
									console.error('处理位置信息时出错:', error);
									uni.showToast({
										title: '位置信息处理失败',
										icon: 'none',
										duration: 2000
									});
								}
							},
							fail: function (err) {
								console.error('选择位置失败:', err);
								uni.showToast({
									title: '选择位置失败，请重试',
									icon: 'none',
									duration: 2000
								});
							}
						});
					}, 300);
				},
				fail(err) {
					console.error('位置授权失败:', err)
					uni.showToast({
						title: '请授权位置信息',
						icon: 'none',
						duration: 2000
					});
				}
			})
			// #endif
			// #ifdef APP
			setTimeout(() => {
				try {
					uni.chooseLocation({
						success: function (res) {
							console.log('APP选择位置成功:', res)
							try {
								that.form.address = (res.name && typeof res.name === 'string') ? res.name : '选择的位置'
								that.form.lng = (res.longitude && typeof res.longitude === 'number') ? res.longitude.toString() : ''
								that.form.lat = (res.latitude && typeof res.latitude === 'number') ? res.latitude.toString() : ''

								uni.showToast({
									title: '位置选择成功',
									icon: 'success',
									duration: 1500
								});
							} catch (error) {
								console.error('APP处理位置信息时出错:', error);
								that.form.address = '位置信息'
								that.form.lng = ''
								that.form.lat = ''

								uni.showToast({
									title: '位置信息处理失败，请重新选择',
									icon: 'none',
									duration: 2500
								});
							}
						},
						fail: function (err) {
							console.error('APP选择位置失败:', err);
							uni.showToast({
								title: '选择位置失败，请重试',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} catch (globalError) {
					console.error('APP端chooseLocation调用失败:', globalError);
					uni.showToast({
						title: '地图功能暂时不可用',
						icon: 'none',
						duration: 2000
					});
				}
			}, 500);
			// #endif
		},

		confirmCity(Array) {
			// 构建完整的省市区信息
			const selectedItems = Array.value.map((item, index) => {
				if (item == undefined) {
					return this.columnsCity[index][0] || {};
				} else {
					return item;
				}
			});

			// 保存完整的省市区信息
			this.selectedProvince = selectedItems[0]?.title || '';
			this.selectedCity = selectedItems[1]?.title || '';
			this.selectedDistrict = selectedItems[2]?.title || '';

			// 构建显示文本：省,市,区 (参考 master_Info.vue)
			this.form.city = selectedItems
				.map(item => item.title || '')
				.filter(title => title)
				.join(',');

			// 构建ID数组，传参时使用最后一级（区县）的ID
			const cityIds = selectedItems.map(item => item.id || 0);

			// 根据需求，传参是传ID，这里保存最后一级的ID（区县ID）
			this.form.cityId = cityIds[cityIds.length - 1] || cityIds[0];

			this.showCity = false;

			// Get city info from API to display trueName
			if (this.form.cityId) {
				this.getCityInfo(this.form.cityId);
			}
		},

		// Get city information from API
		async getCityInfo(cityId) {
			try {
				console.log('Getting city info for ID:', cityId);
				const response = await this.$api.service.getCityDetail(cityId);
				console.log('getCityInfo response:', response);

				if (response && response.code === "200" && response.data) {
					const cityData = response.data;
					console.log('City detail data:', cityData);

					// 使用 API 返回的 trueName 作为显示名称
					this.form.city = cityData.trueName;

					// 保存完整的城市信息，与定位数据格式保持一致
					this.cityDetailInfo = {
						id: cityData.id,
						pid: cityData.pid,
						title: cityData.title,
						trueName: cityData.trueName,
						mergename: cityData.mergename,
						level: cityData.level,
						pinyin: cityData.pinyin,
						code: cityData.code,
						zip: cityData.zip,
						first: cityData.first,
						lng: cityData.lng,
						lat: cityData.lat,
						status: cityData.status
					};

				} else {
					console.error('API返回格式错误:', response);
					// API失败时，使用本地选择的最后一级名称
					if (this.selectedDistrict) {
						this.form.city = this.selectedDistrict;
					} else if (this.selectedCity) {
						this.form.city = this.selectedCity;
					} else {
						this.form.city = this.selectedProvince;
					}
				}
			} catch (error) {
				console.error('获取城市信息失败:', error);
				// API失败时，使用本地选择的最后一级名称
				if (this.selectedDistrict) {
					this.form.city = this.selectedDistrict;
				} else if (this.selectedCity) {
					this.form.city = this.selectedCity;
				} else {
					this.form.city = this.selectedProvince;
				}
			}
		},

		getCity(e) {
			this.$api.service.getCity(e).then(res => {
				console.log('getCity response:', res)

				// 根据您提供的数据结构处理
				if (res.code === "200" && res.data) {
					// 转换数据格式，将trueName转换为title以适配picker组件
					const provinces = res.data.map(item => ({
						...item,
						title: item.trueName || item.title
					}));
					this.columnsCity[0] = provinces;

					// 初始化第一个省份的城市数据
					if (provinces.length > 0 && provinces[0].children) {
						const cities = provinces[0].children.map(item => ({
							...item,
							title: item.trueName || item.title
						}));
						this.columnsCity[1] = cities;

						// 初始化第一个城市的区县数据
						if (cities.length > 0 && cities[0].children) {
							const districts = cities[0].children.map(item => ({
								...item,
								title: item.trueName || item.title
							}));
							this.columnsCity[2] = districts;
						} else {
							this.columnsCity[2] = [];
						}
					} else {
						this.columnsCity[1] = [];
						this.columnsCity[2] = [];
					}
				} else {
					console.error('Unexpected data format:', res);
				}
				this.flag = true;
			}).catch(err => {
				console.error('Failed to fetch city data:', err)
			})
		},

		changeHandler(e) {
			const { columnIndex, index, picker = this.$refs.uPicker } = e;
			if (columnIndex === 0) {
				// 选择省份时，从children中获取对应的城市列表 (参考 master_Info.vue)
				const selectedProvince = this.columnsCity[0][index];
				if (selectedProvince && selectedProvince.children) {
					const cities = selectedProvince.children.map(item => ({
						...item,
						title: item.trueName || item.title
					}));
					picker.setColumnValues(1, cities);
					this.columnsCity[1] = cities;

					// 同时更新第一个城市的区县数据
					if (cities.length > 0 && cities[0].children) {
						const districts = cities[0].children.map(item => ({
							...item,
							title: item.trueName || item.title
						}));
						picker.setColumnValues(2, districts);
						this.columnsCity[2] = districts;
					} else {
						picker.setColumnValues(2, []);
						this.columnsCity[2] = [];
					}
				} else {
					picker.setColumnValues(1, []);
					picker.setColumnValues(2, []);
					this.columnsCity[1] = [];
					this.columnsCity[2] = [];
				}
			} else if (columnIndex === 1) {
				// 选择城市时，从children中获取对应的区县列表 (参考 master_Info.vue)
				const selectedCity = this.columnsCity[1][index];
				if (selectedCity && selectedCity.children) {
					const districts = selectedCity.children.map(item => ({
						...item,
						title: item.trueName || item.title
					}));
					picker.setColumnValues(2, districts);
					this.columnsCity[2] = districts;
				} else {
					picker.setColumnValues(2, []);
					this.columnsCity[2] = [];
				}
			}
		},

		confirmSelection() {
			if (!this.form.city) {
				uni.showToast({
					icon: 'none',
					title: '请选择所在区域',
					duration: 1500
				})
				return
			}

			// 构建与定位数据格式一致的信息
			const cityInfo = {
				city: this.form.city, // 显示名称（如：临泉县）
				cityId: this.form.cityId,
				address: this.form.address,
				lng: this.form.lng,
				lat: this.form.lat,
				fullPath: `${this.selectedProvince},${this.selectedCity},${this.selectedDistrict}`.replace(/^,|,$/g, ''), // 完整路径
				province: this.selectedProvince,
				cityName: this.selectedCity,
				district: this.selectedDistrict,
				type: 'manual', // 标记为手动选择
				timestamp: Date.now()
			};

			// 如果有API返回的详细信息，添加到数据中
			if (this.cityDetailInfo) {
				cityInfo.detail = this.cityDetailInfo;
				// 使用API返回的经纬度（如果有的话）
				if (this.cityDetailInfo.lng && this.cityDetailInfo.lat) {
					cityInfo.lng = this.cityDetailInfo.lng;
					cityInfo.lat = this.cityDetailInfo.lat;
				}
			}

			// 使用LocationManager的方法保存数据，确保lng、lat、locationData都被正确存储
			locationManager.saveUserSelectedCity(cityInfo);

			// 同时保存到旧的存储格式以保持兼容性
			uni.setStorageSync('city', {
				city_id: this.form.cityId,
				position: this.form.city
			});

			uni.showToast({
				title: '选择成功',
				icon: 'success',
				duration: 1500
			});

			// Navigate back and update service page
			uni.navigateBack({
				success: () => {
					// Emit event to update service page
					uni.$emit('citySelected', {
						city: this.form.city,
						cityId: this.form.cityId
					});
				}
			});
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	height: 100vh;
	background-color: #fff;

	.top {
		width: 750rpx;
		height: 58rpx;
		background: #FFF7F1;
		font-size: 28rpx;
		font-weight: 400;
		color: #FE921B;
		line-height: 58rpx;
		text-align: center;
	}

	.btn {
		margin: 0 auto;
		margin-top: 88rpx;
		width: 690rpx;
		height: 98rpx;
		background: #2E80FE;
		border-radius: 50rpx 50rpx 50rpx 50rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 98rpx;
		text-align: center;
	}

	.main {
		padding: 0 30rpx;

		.main_item {
			padding: 40rpx 0;
			border-bottom: 2rpx solid #E9E9E9;
			display: flex;
			align-items: center;
			position: relative;

			.name {
				min-width: 112rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				margin-right: 40rpx;
			}

			.address {
				font-size: 28rpx;
				font-weight: 400;
				color: #ADADAD;
			}

			image {
				width: 23rpx;
				height: 27rpx;
				position: absolute;
				right: 0;
				top: 46rpx;
			}

			input {
				width: 450rpx;
			}
		}

		/* 新增定位项目样式 */
		.location-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;

			.location-left {
				display: flex;
				align-items: center;
				min-width: 140rpx;

				.location-icon {
					font-size: 32rpx;
					margin-right: 10rpx;
				}

				.location-text {
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
				}
			}

			.location-center {
				flex: 1;
				margin: 0 20rpx;

				.address-text {
					font-size: 28rpx;
					font-weight: 400;
					color: #666666;
					line-height: 1.4;
					word-break: break-all;
				}
			}

			.location-right {
				.relocate-btn {
					padding: 12rpx 24rpx;
					background: #2E80FE;
					border-radius: 30rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #FFFFFF;
					text-align: center;
					min-width: 120rpx;
				}
			}
		}
	}
}
</style>
