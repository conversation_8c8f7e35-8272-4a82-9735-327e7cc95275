{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/App.vue?dbd3", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/App.vue?a9cc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/App.vue?6c30"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "component", "upload", "prototype", "$navigateTo", "url", "uni", "navigateTo", "fail", "err", "console", "error", "showToast", "title", "errMsg", "icon", "$api", "api", "$util", "util", "config", "productionTip", "App", "mpType", "app", "store", "$mount", "data", "registrationID", "connectStatus", "mounted", "onLaunch", "configInfo", "$store", "key", "val", "arr", "commonOptions", "channel_id", "systemInfo", "platform", "appVersion", "appName", "primaryColor", "onShow", "onHide", "methods", "getRegistrationID", "getNotificationEnabled", "noticMsgTool", "getBaseConfig", "checkAppUpdateOnLaunch", "initLocation", "locationManager", "silent", "checkLocationUpdate", "cachedData"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAgD;AAC3G;AAEA;AACA;AAIA;AAEA;AAA6B;AAAA;AAX7B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAY3DC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;AACdF,YAAG,CAACG,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;AAC/B;AACAJ,YAAG,CAACK,SAAS,CAACC,WAAW,GAAG,UAACC,GAAG,EAAK;EACnCC,GAAG,CAACC,UAAU,CAAC;IACbF,GAAG,EAAHA,GAAG;IACHG,IAAI,EAAE,cAACC,GAAG,EAAK;MACbC,OAAO,CAACC,KAAK,CAAC,OAAO,EAAEF,GAAG,CAAC;MAC3BH,GAAG,CAACM,SAAS,CAAC;QACZC,KAAK,EAAE,QAAQ,GAAGJ,GAAG,CAACK,MAAM;QAC5BC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC;AACDjB,YAAG,CAACK,SAAS,CAACa,IAAI,GAAGC,cAAG;AACxBnB,YAAG,CAACK,SAAS,CAACe,KAAK,GAAGC,eAAI;AAI1BrB,YAAG,CAACsB,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAI1B,YAAG,iCACfwB,YAAG;EACNG,KAAK,EAALA;AAAK,GACJ;AACF,UAAAD,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;AC1CZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAk1B,CAAgB,k2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACCt2B;AACA;AACA;AACA;AAAA,eAOA;EACA;EACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;MAAA;QAAA;UAAA;YAAA;cASApB;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAqB;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAgEAC;cACA;gBACAC;kBACAC;kBACAC;gBACA;cACA;cAEAC;cACAA;gBACA;gBACA;kBACAH;oBACAC;oBACAC;kBACA;gBACA;cACA;cACAE;cAAA,wBAGAA,cADAC;cAEA;gBACAD;gBACAJ;kBACAC;kBACAC;gBACA;cACA;;cAEA;cACAzB;cACA;gBACA6B;gBACA7B;kBACA8B;kBACAC;kBACAC;gBACA;cACA;gBACAhC;cACA;cAWAA;cAAA,wBAKAuB,wCADAU;cAAA,KAEAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAlC;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAmC;IACAnC;EACA;EACAoC;IACA;IACAC,iDAaA;IAEA;IACAC,2DAqBA;IAEA;IACAC,uCAmDA;IAEAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAlC;cAAA;gBAAAI;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBACAY;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAgB;MAAA;QAAA;UAAA;YAAA;cAAA;gBA+CAzC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA0C;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA1C;gBACA;gBAAA;gBAAA,OACA2C;kBAAAC;gBAAA;cAAA;gBACA5C;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA6C;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA9C;gBAAA;gBAAA,OACA2C;kBAAAC;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA5C;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9WA;AAAA;AAAA;AAAA;AAAikD,CAAgB,qhDAAG,EAAC,C;;;;;;;;;;;ACArlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';import Vue from 'vue'\r\nimport App from './App'\r\n// import './index.css'\r\nimport api from \"api/index.js\"\r\nimport util from \"@/utils/index.js\"\r\n\r\n\r\n\r\nimport store from \"@/store/index.js\"\r\nimport upload from '@/components/upload.vue'\r\nimport uView from \"uview-ui\";\r\n\r\nVue.use(uView);\r\nVue.component('upload', upload)\r\n// 挂载全局跳转方法\r\nVue.prototype.$navigateTo = (url) => {\r\n  uni.navigateTo({\r\n    url,\r\n    fail: (err) => {\r\n      console.error(\"跳转失败:\", err);\r\n      uni.showToast({\r\n        title: \"跳转失败: \" + err.errMsg,\r\n        icon: \"none\"\r\n      });\r\n    }\r\n  });\r\n};\r\nVue.prototype.$api = api\r\nVue.prototype.$util = util\r\n\r\n\r\n\r\nVue.config.productionTip = false\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n\t...App,\r\n\tstore,\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport $api from \"@/api/index.js\"\r\n\timport $store from \"@/store/index.js\"\r\n\timport appUpdate from '@/utils/app-update.js'\r\n\timport locationManager from \"@/utils/location-manager.js\"\r\n\r\n\t// #ifdef APP-PLUS\r\n\tvar jpushModule = uni.requireNativePlugin(\"JG-JPush\");\r\n\tvar audioObj = uni.getBackgroundAudioManager();\r\n\t// #endif\r\n\r\n\texport default {\r\n\t\t// globalData: {\r\n\t\t//     primaryColor: '#A40035', // Default fallback, matching your sidebar’s color\r\n\t\t//   },\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tregistrationID: '',\r\n\t\t\t\tconnectStatus: ''\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tasync mounted() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tif (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {\r\n\t\t\t\twindow.entryUrl = window.location.href.split('#')[0]\r\n\t\t\t}\r\n\t\t\tif (window.location.href.indexOf('?#') < 0) {\r\n\t\t\t\twindow.location.href = window.location.href.replace(\"#\", \"?#\");\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tconsole.log('App mounted')\r\n\t\t},\r\n\t\r\n\t\tasync onLaunch() {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// 检查极光推送模块是否可用\r\n\t\t\tif (jpushModule) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 初始化极光推送\r\n\t\t\t\t\tjpushModule.initJPushService();\r\n\t\t\t\t\tjpushModule.setLoggerEnable(true);\r\n\t\t\t\t\tplus.screen.lockOrientation(\"portrait-primary\");\r\n\r\n\t\t\t\t\t// 监听极光推送连接状态\r\n\t\t\t\t\tthis.getNotificationEnabled();\r\n\t\t\t\t\tjpushModule.addConnectEventListener(result => {\r\n\t\t\t\t\t\tlet connectEnable = result.connectEnable\r\n\t\t\t\t\t\tuni.$emit('connectStatusChange', connectEnable)\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 监听推送消息\r\n\t\t\t\t\tjpushModule.addNotificationListener(result => {\r\n\t\t\t\t\t\tjpushModule.setBadge(0);\r\n\t\t\t\t\t\tplus.runtime.setBadgeNumber(0);\r\n\t\t\t\t\t\tlet notificationEventType = result.notificationEventType\r\n\t\t\t\t\t\tconsole.log(\"通知\", result, notificationEventType)\r\n\r\n\t\t\t\t\t\t// 点击事件处理\r\n\t\t\t\t\t\tif (notificationEventType == 'notificationOpened') {\r\n\t\t\t\t\t\t\t// 这里可以根据推送内容进行页面跳转\r\n\t\t\t\t\t\t\tlet extras = result.extras || {};\r\n\t\t\t\t\t\t\tif (extras.page) {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: extras.page\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 监听连接状态变化\r\n\t\t\t\t\tuni.$on('connectStatusChange', (connectStatus) => {\r\n\t\t\t\t\t\tvar connectStr = ''\r\n\t\t\t\t\t\tif (connectStatus == true) {\r\n\t\t\t\t\t\t\tconnectStr = '已连接'\r\n\t\t\t\t\t\t\tthis.getRegistrationID()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconnectStr = '未连接'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconsole.log('监听到了连接状态变化 --- ', connectStr)\r\n\t\t\t\t\t\tthis.connectStatus = connectStr\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\t// 检查推送状态\r\n\t\t\t\t\tjpushModule.isPushStopped(res => {\r\n\t\t\t\t\t\tconst { code } = res\r\n\t\t\t\t\t\tconsole.log(res, '推送连接状态');\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tconsole.log('极光推送初始化成功');\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('极光推送初始化失败:', error);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconsole.warn('极光推送模块未找到，请检查插件配置或使用自定义调试基座');\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\r\n\t\t\tlet configInfo = uni.getStorageSync('configInfo') || ''\r\n\t\t\tif (configInfo) {\r\n\t\t\t\t$store.commit('updateConfigItem', {\r\n\t\t\t\t\tkey: 'configInfo',\r\n\t\t\t\t\tval: configInfo\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\tlet arr = ['autograph', 'userInfo', 'location', 'appLogin']\r\n\t\t\tarr.map(key => {\r\n\t\t\t\tlet val = uni.getStorageSync(key) || ''\r\n\t\t\t\tif (val) {\r\n\t\t\t\t\t$store.commit('updateUserItem', {\r\n\t\t\t\t\t\tkey,\r\n\t\t\t\t\t\tval\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tlet commonOptions = $store.state.user.commonOptions\r\n\t\t\tlet {\r\n\t\t\t\tchannel_id = 0\r\n\t\t\t} = commonOptions\r\n\t\t\tif (channel_id) {\r\n\t\t\t\tcommonOptions.channel_id = 0\r\n\t\t\t\t$store.commit('updateUserItem', {\r\n\t\t\t\t\tkey: 'commonOptions',\r\n\t\t\t\t\tval: commonOptions\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\t// 检查APP更新（启动时检查，有更新时弹窗提醒）\r\n\t\t\tconsole.log('=== 准备检查更新，当前平台信息 ===')\r\n\t\t\ttry {\r\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\t\t\tconsole.log('系统信息:', {\r\n\t\t\t\t\tplatform: systemInfo.platform,\r\n\t\t\t\t\tappVersion: systemInfo.appVersion,\r\n\t\t\t\t\tappName: systemInfo.appName\r\n\t\t\t\t})\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取系统信息失败:', error)\r\n\t\t\t}\r\n\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tconsole.log('=== APP启动完成，准备延迟检查更新 ===')\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tconsole.log('=== 3秒延迟结束，开始执行更新检查 ===')\r\n\t\t\t\tthis.checkAppUpdateOnLaunch()\r\n\t\t\t}, 3000) // 延迟3秒检查，避免影响启动速度\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tconsole.log('=== 非APP环境，跳过更新检查 ===')\r\n\t\t\t// #endif\r\n\r\n\t\t\tlet {\r\n\t\t\t\tprimaryColor = ''\r\n\t\t\t} = $store.state.config.configInfo\r\n\t\t\tif (primaryColor) return\r\n\t\t\tawait this.getBaseConfig()\r\n\r\n\t\t\t// 初始化定位\r\n\t\t\tthis.initLocation()\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t\t// 应用从后台回到前台时，检查定位是否需要更新\r\n\t\t\tthis.checkLocationUpdate()\r\n\t\t},\r\n\t\tonHide() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取registrationID\r\n\t\t\tgetRegistrationID() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (jpushModule) {\r\n\t\t\t\t\tjpushModule.getRegistrationID(result => {\r\n\t\t\t\t\t\tlet registerID = result.registerID\r\n\t\t\t\t\t\tconsole.log('registrationID:', registerID)\r\n\t\t\t\t\t\tthis.registrationID = registerID\r\n\t\t\t\t\t\tuni.setStorageSync(\"registerID\", registerID)\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('极光推送模块未找到，无法获取registrationID');\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t// 检查通知权限\r\n\t\t\tgetNotificationEnabled() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (jpushModule) {\r\n\t\t\t\t\tif (uni.getSystemInfoSync().platform == \"ios\") {\r\n\t\t\t\t\t\tjpushModule.requestNotificationAuthorization((result) => {\r\n\t\t\t\t\t\t\tlet status = result.status\r\n\t\t\t\t\t\t\tif (status < 2) {\r\n\t\t\t\t\t\t\t\tthis.noticMsgTool()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tjpushModule.isNotificationEnabled((result) => {\r\n\t\t\t\t\t\t\tif (result.code == 0) {\r\n\t\t\t\t\t\t\t\tthis.noticMsgTool()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('极光推送模块未找到，无法检查通知权限');\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t// 引导用户开启通知权限\r\n\t\t\tnoticMsgTool() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (uni.getSystemInfoSync().platform == \"ios\") {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '通知权限开启提醒',\r\n\t\t\t\t\t\tcontent: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '去设置',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tvar app = plus.ios.invoke('UIApplication', 'sharedApplication');\r\n\t\t\t\t\t\t\t\tvar setting = plus.ios.invoke('NSURL', 'URLWithString:', 'app-settings:');\r\n\t\t\t\t\t\t\t\tplus.ios.invoke(app, 'openURL:', setting);\r\n\t\t\t\t\t\t\t\tplus.ios.deleteObject(setting);\r\n\t\t\t\t\t\t\t\tplus.ios.deleteObject(app);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar main = plus.android.runtimeMainActivity();\r\n\t\t\t\t\tvar pkName = main.getPackageName();\r\n\t\t\t\t\tvar uid = main.getApplicationInfo().plusGetAttribute(\"uid\");\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '通知权限开启提醒',\r\n\t\t\t\t\t\tcontent: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '去设置',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tvar Intent = plus.android.importClass('android.content.Intent');\r\n\t\t\t\t\t\t\t\tvar Build = plus.android.importClass(\"android.os.Build\");\r\n\t\t\t\t\t\t\t\tif (Build.VERSION.SDK_INT >= 26) {\r\n\t\t\t\t\t\t\t\t\tvar intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');\r\n\t\t\t\t\t\t\t\t\tintent.putExtra('android.provider.extra.APP_PACKAGE', pkName);\r\n\t\t\t\t\t\t\t\t} else if (Build.VERSION.SDK_INT >= 21) {\r\n\t\t\t\t\t\t\t\t\tvar intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');\r\n\t\t\t\t\t\t\t\t\tintent.putExtra(\"app_package\", pkName);\r\n\t\t\t\t\t\t\t\t\tintent.putExtra(\"app_uid\", uid);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tvar Settings = plus.android.importClass(\"android.provider.Settings\");\r\n\t\t\t\t\t\t\t\t\tvar Uri = plus.android.importClass(\"android.net.Uri\");\r\n\t\t\t\t\t\t\t\t\tintent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);\r\n\t\t\t\t\t\t\t\t\tvar uri = Uri.fromParts(\"package\", pkName, null);\r\n\t\t\t\t\t\t\t\t\tintent.setData(uri);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tmain.startActivity(intent);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\tasync getBaseConfig() {\r\n\t\t\t\tlet config = await $api.base.getConfig()\r\n\t\t\t\tif (!config.primaryColor) {\r\n\t\t\t\t\tconfig.primaryColor = '#A40035'\r\n\t\t\t\t}\r\n\t\t\t\tif (!config.subColor) {\r\n\t\t\t\t\tconfig.subColor = '#F1C06B'\r\n\t\t\t\t}\r\n\t\t\t\tlet configInfo = Object.assign($store.state.config.configInfo, config)\r\n\t\t\t\t$store.commit('updateConfigItem', {\r\n\t\t\t\t\tkey: 'configInfo',\r\n\t\t\t\t\tval: configInfo\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 启动时检查APP更新\r\n\t\t\t * 如果有更新则弹窗提醒，没有更新则静默处理\r\n\t\t\t */\r\n\t\t\tasync checkAppUpdateOnLaunch() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('=== 开始检查APP更新 ===')\r\n\r\n\t\t\t\t\t// 获取当前版本\r\n\t\t\t\t\tconst currentVersion = await appUpdate.getCurrentVersion()\r\n\t\t\t\t\tconsole.log('当前版本:', currentVersion)\r\n\r\n\t\t\t\t\t// 调用后端接口检查更新\r\n\t\t\t\t\tconst response = await $api.user.checkAppVersion({\r\n\t\t\t\t\t\tversion: currentVersion,\r\n\t\t\t\t\t\tplatform: 1 // 师傅端\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tconsole.log('版本检查响应:', JSON.stringify(response))\r\n\r\n\t\t\t\t\tif (response.code === '200' && response.data) {\r\n\t\t\t\t\t\tconst updateInfo = response.data\r\n\t\t\t\t\t\tconsole.log('更新信息:', JSON.stringify(updateInfo))\r\n\r\n\t\t\t\t\t\tif (updateInfo.needUpdate) {\r\n\t\t\t\t\t\t\tconsole.log('=== 发现新版本，显示更新提醒 ===')\r\n\t\t\t\t\t\t\t// 有更新时显示弹窗\r\n\t\t\t\t\t\t\tappUpdate.showUpdateDialog(updateInfo)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log('=== 已是最新版本，无需更新 ===')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('检查更新失败:', response.msg || '未知错误')\r\n\t\t\t\t\t\t// 如果是网络错误或服务器错误，可以尝试使用默认的更新检查\r\n\t\t\t\t\t\tconsole.log('尝试使用默认更新检查方法...')\r\n\t\t\t\t\t\tawait appUpdate.checkUpdate({ silent: true, showLoading: false })\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('检查更新异常:', error)\r\n\t\t\t\t\t// 如果出现异常，尝试使用默认的更新检查\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tconsole.log('异常后尝试使用默认更新检查方法...')\r\n\t\t\t\t\t\tawait appUpdate.checkUpdate({ silent: true, showLoading: false })\r\n\t\t\t\t\t} catch (fallbackError) {\r\n\t\t\t\t\t\tconsole.error('默认更新检查也失败:', fallbackError)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\tconsole.log('非APP环境，跳过更新检查')\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 初始化定位\r\n\t\t\t */\r\n\t\t\tasync initLocation() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('开始初始化定位...')\r\n\t\t\t\t\t// 静默获取定位，不显示loading\r\n\t\t\t\t\tawait locationManager.getLocation({ silent: true })\r\n\t\t\t\t\tconsole.log('定位初始化完成')\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('定位初始化失败:', error)\r\n\t\t\t\t\t// 定位失败不影响应用启动\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 检查定位更新\r\n\t\t\t */\r\n\t\t\tasync checkLocationUpdate() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 检查是否需要更新定位（超过5分钟自动更新）\r\n\t\t\t\t\tconst cachedData = locationManager._getFromStorage()\r\n\t\t\t\t\tif (!cachedData || (Date.now() - cachedData.cacheTime > 5 * 60 * 1000)) {\r\n\t\t\t\t\t\tconsole.log('定位缓存过期，重新获取定位...')\r\n\t\t\t\t\t\tawait locationManager.getLocation({ silent: true })\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('检查定位更新失败:', error)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"uview-ui/index.scss\";\r\n\t@import \"/styles/index.wxss\";\r\n\r\n\t/* #ifdef H5 */\r\n\tuni-page-head {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t/* #endif */\r\n\tpage {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #222;\r\n\t\tline-height: 1.5;\r\n\t\tbackground: #fff;\r\n\t\tfont-family: 'MyFont';\r\n\t}\r\n\r\n\tinput {\r\n\t\t// font-family: PingFangSC-Medium, PingFang SC, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;\r\n\t}\r\n\r\n\tinput::-webkit-input-placeholder {\r\n\t\t/* WebKit browsers */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tinput:-moz-placeholder {\r\n\t\t/* Mozilla Firefox 4 to 18 */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tinput::-moz-placeholder {\r\n\t\t/* Mozilla Firefox 19+ */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tinput:-ms-input-placeholder {\r\n\t\t/* Internet Explorer 10+ */\r\n\t\tcolor: #A9A9A9;\r\n\t}\r\n\r\n\tview {\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\timage {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t/*隐藏滚动条*/\r\n\t::-webkit-scrollbar {\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tcolor: transparent;\r\n\t}\r\n\r\n\t/* #ifdef MP-BAIDU */\r\n\t.swan-button.swan-button-radius-ios {\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756176211583\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}