<view class="page data-v-b81bd106"><block wx:if="{{flag}}"><u-picker vue-id="4b5d4dae-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeHandler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-b81bd106 vue-ref" bind:__l="__l"></u-picker></block><view class="top data-v-b81bd106">请选择你所在的城市以获取周边服务</view><view class="main data-v-b81bd106"><view class="main_item location-item data-v-b81bd106"><view class="location-left data-v-b81bd106"><view class="location-icon data-v-b81bd106">📍</view><view class="location-text data-v-b81bd106">当前定位</view></view><view class="location-center data-v-b81bd106"><view class="address-text data-v-b81bd106">{{form.address}}</view></view><view data-event-opts="{{[['tap',[['relocate',['$event']]]]]}}" class="location-right data-v-b81bd106" bindtap="__e"><view class="relocate-btn data-v-b81bd106">重新定位</view></view></view><view class="main_item data-v-b81bd106"><view class="name data-v-b81bd106">所在区域</view><input type="text" placeholder="请选择所在区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e1',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-b81bd106"/></view></view><view data-event-opts="{{[['tap',[['confirmSelection',['$event']]]]]}}" class="btn data-v-b81bd106" bindtap="__e">确定</view></view>