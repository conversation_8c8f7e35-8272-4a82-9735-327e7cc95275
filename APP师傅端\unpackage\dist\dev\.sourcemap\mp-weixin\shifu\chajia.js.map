{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?d719", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?d5b3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?88a1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?4614", "uni-app:///shifu/chajia.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?f42b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/chajia.vue?f147"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "orderInfo", "orderId", "commissionRate", "diffApplyForm", "diffAmount", "reasonType", "reasonDetail", "partsWarrantyDuration", "partsimgs", "diffApplyRules", "required", "message", "trigger", "validator", "pay_typeArr", "computed", "serviceFee", "actualAmount", "onLoad", "console", "uni", "icon", "title", "setTimeout", "onReady", "methods", "formatAmount", "onDiffAmountChange", "goBack", "loadOrderInfo", "orderDetails", "getOrderStatusText", "getDiffStatusText", "formatWarrantyDate", "getRemainingWarrantyDays", "getScrollViewHeight", "imgUploadDiff", "imgtype", "showDiffCancelModal", "content", "confirmText", "cancelText", "success", "diffCancel", "id", "res", "diffApplyConfirm", "partsImgsString", "actualDiffAmount", "partsImgs", "partsWarrantyPeriod"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,oTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eC+Iz2B;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAL;UACAM;UACAC;UACAC;QACA;UACAC;YACA;UACA;UACAF;UACAC;QACA;QACAN;UACAI;UACAC;UACAC;QACA;QACAL;UACAG;UACAC;UACAC;QACA;UACAC;YACA;UACA;UACAF;UACAC;QACA;MACA;MACAE;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACAC;MACA;QACA;MACA;IACA;MACAA;IACA;IAEA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACAR;IACA;IACAS;MACAR;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAC;kBACA;oBACA;kBACA;oBACAV;sBACAC;sBACAC;oBACA;kBACA;gBACA;kBACAH;kBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;MACA;QACAd;QACA;MACA;IACA;IACAe;MACA;MACA;MACA;MAEA;QACA;MACA;MAEA;MACA;MAEA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QAAAC;MACA;IACA;IACAC;MAAA;MACAlB;QACAE;QACAiB;QACAC;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFAC;gBAGA;kBACAzB;oBACAE;oBACAD;kBACA;kBACA;gBACA;kBACAD;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA1B;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKAyB;kBAAA;gBAAA;gBAEA3C;gBACAY;gBACAgC;gBAAA;gBAAA,OAEA;kBACA/C;kBACAG;kBACAC;kBACAC;kBACA2C;kBACAC;gBACA;cAAA;gBAPAL;gBASA;kBACAzB;oBACAE;oBACAD;kBACA;kBACAE;oBACA;kBACA;gBACA;kBACAH;oBACAE;oBACAD;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;kBACAE;kBACAD;gBACA;gBACAF;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnaA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/chajia.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/chajia.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./chajia.vue?vue&type=template&id=7ae43980&scoped=true&\"\nvar renderjs\nimport script from \"./chajia.vue?vue&type=script&lang=js&\"\nexport * from \"./chajia.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chajia.vue?vue&type=style&index=0&id=7ae43980&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7ae43980\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/chajia.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=template&id=7ae43980&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--form/u--form\" */ \"uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-form-item/u-form-item\" */ \"uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--textarea/u--textarea\" */ \"uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.diffApplyForm.diffAmount && parseFloat(_vm.diffApplyForm.diffAmount) > 0\n  var m1 = m0 ? _vm.formatAmount(_vm.diffApplyForm.diffAmount) : null\n  var m2 = m0 ? _vm.formatAmount(_vm.serviceFee) : null\n  var m3 = m0 ? _vm.formatAmount(_vm.actualAmount) : null\n  var g0 =\n    _vm.orderInfo &&\n    _vm.orderInfo.orderDiffPriceList &&\n    _vm.orderInfo.orderDiffPriceList.length > 0\n  var g1 = g0 ? _vm.orderInfo.orderDiffPriceList.length : null\n  var m4 = g0\n    ? _vm.getScrollViewHeight(_vm.orderInfo.orderDiffPriceList.length)\n    : null\n  var l0 = g0\n    ? _vm.__map(\n        _vm.orderInfo.orderDiffPriceList,\n        function (diffItem, diffIndex) {\n          var $orig = _vm.__get_orig(diffItem)\n          var m5 = _vm.getDiffStatusText(diffItem.status)\n          var m6 = _vm.formatWarrantyDate(diffItem.partsWarrantyPeriod)\n          var m7 = diffItem.partsWarrantyPeriod\n            ? _vm.getRemainingWarrantyDays(diffItem.partsWarrantyPeriod)\n            : null\n          return {\n            $orig: $orig,\n            m5: m5,\n            m6: m6,\n            m7: m7,\n          }\n        }\n      )\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        g0: g0,\n        g1: g1,\n        m4: m4,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"content\">\n\t\t\t<!-- 订单信息卡片 -->\n\t\t\t<view class=\"card order-info\" v-if=\"orderInfo\">\n\t\t\t\t<!-- <view class=\"order-header\">\n\t\t\t\t\t<view class=\"order-no\">订单号：{{ orderInfo.orderCode }}</view>\n\t\t\t\t\t<view class=\"order-status-tag\">{{ getOrderStatusText(orderInfo.payType) }}</view>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"order-detail\">\n\t\t\t\t\t<view class=\"goods-info\">\n\t\t\t\t\t\t<image :src=\"orderInfo.goodsCover\" class=\"goods-image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"goods-text\">\n\t\t\t\t\t\t\t<text class=\"goods-name\">{{ orderInfo.goodsName }}</text>\n\t\t\t\t\t\t\t<text class=\"goods-price\">￥{{ orderInfo.coachServicePrice }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 申请差价表单 -->\n\t\t\t<view class=\"card form-container\">\n\t\t\t\t<view class=\"form-title\">申请差价</view>\n\t\t\t\t<u--form labelPosition=\"left\" :model=\"diffApplyForm\" :rules=\"diffApplyRules\" ref=\"diffApplyForm\">\n\t\t\t\t\t<u-form-item label=\"差价金额\" prop=\"diffAmount\" ref=\"item1\" class=\"form-item\">\n\t\t\t\t\t\t<u--input v-model=\"diffApplyForm.diffAmount\" placeholder=\"请输入差价金额\" type=\"number\"\n\t\t\t\t\t\t\tborder=\"none\" @input=\"onDiffAmountChange\"></u--input>\n\t\t\t\t\t</u-form-item>\n\n\t\t\t\t\t<!-- 费用明细显示区域 -->\n\t\t\t\t\t<transition name=\"fade-slide\">\n\t\t\t\t\t\t<view class=\"fee-detail-container\" v-if=\"diffApplyForm.diffAmount && parseFloat(diffApplyForm.diffAmount) > 0\">\n\t\t\t\t\t\t\t<view class=\"fee-detail-title\">费用明细</view>\n\t\t\t\t\t\t\t<view class=\"fee-detail-content\">\n\t\t\t\t\t\t\t\t<view class=\"fee-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"fee-label\">差价金额</view>\n\t\t\t\t\t\t\t\t\t<view class=\"fee-value primary\">¥{{ formatAmount(diffApplyForm.diffAmount) }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"fee-item\">\n\t\t\t\t\t\t\t\t\t<view class=\"fee-label\">服务费</view>\n\t\t\t\t\t\t\t\t\t<view class=\"fee-value danger\">-¥{{ formatAmount(serviceFee) }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"fee-divider\"></view>\n\t\t\t\t\t\t\t\t<view class=\"fee-item total\">\n\t\t\t\t\t\t\t\t\t<view class=\"fee-label\">预估到手金额</view>\n\t\t\t\t\t\t\t\t\t<view class=\"fee-value success\">¥{{ formatAmount(actualAmount) }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</transition>\n\n\t\t\t\t\t<u-form-item label=\"差价原因\" class=\"form-item\">\n\t\t\t\t\t\t<view class=\"reason-type-display\">\n\t\t\t\t\t\t\t<text class=\"reason-type-text\">配件不符合</text>\n\t\t\t\t\t\t\t<view class=\"reason-type-badge\">类型: 1</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t\t<u-form-item label=\"原因详情\" prop=\"reasonDetail\" ref=\"item3\" class=\"form-item\">\n\t\t\t\t\t\t<u--textarea v-model=\"diffApplyForm.reasonDetail\" placeholder=\"请输入差价原因详情\"\n\t\t\t\t\t\t\tcount></u--textarea>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t\t<u-form-item label=\"配件质保时长\" prop=\"partsWarrantyDuration\" ref=\"item4\" class=\"form-item\">\n\t\t\t\t\t\t<view class=\"warranty-input-group\">\n\t\t\t\t\t\t\t<u--input v-model=\"diffApplyForm.partsWarrantyDuration\" placeholder=\"请输入质保天数\"\n\t\t\t\t\t\t\t\ttype=\"number\" border=\"none\"></u--input>\n\t\t\t\t\t\t\t<text class=\"unit-text\">天</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t\t<u-form-item label=\"配件图\" class=\"last-form-item form-item\" ref=\"item5\">\n\t\t\t\t\t\t<view class=\"upload-container\">\n\t\t\t\t\t\t\t<upload @upload=\"imgUploadDiff\" @del=\"imgUploadDiff\"\n\t\t\t\t\t\t\t\t:imagelist=\"diffApplyForm.partsimgs\" imgtype=\"partsimgs\" imgclass=\"parts-img\"\n\t\t\t\t\t\t\t\ttext=\"上传配件图\" :imgsize=\"9\"></upload>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</u-form-item>\n\t\t\t\t</u--form>\n\t\t\t</view>\n\n\t\t\t<!-- 差价申请记录 -->\n\t\t\t<view v-if=\"orderInfo && orderInfo.orderDiffPriceList && orderInfo.orderDiffPriceList.length > 0\" class=\"card sub-orders\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">差价申请记录</text>\n\t\t\t\t\t<text class=\"title-count\">({{ orderInfo.orderDiffPriceList.length }})</text>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"sub-scroll-container\" scroll-y=\"true\" :style=\"{height: getScrollViewHeight(orderInfo.orderDiffPriceList.length) + 'rpx'}\">\n\t\t\t\t\t<view class=\"sub-item\" v-for=\"(diffItem, diffIndex) in orderInfo.orderDiffPriceList\" :key=\"diffItem.id\">\n\t\t\t\t\t\t<view class=\"sub-head\">\n\t\t\t\t\t\t\t<view class=\"sub-no\">差价单号：{{ diffItem.diffCode }}</view>\n\t\t\t\t\t\t\t<view class=\"sub-status-tag\" :class=\"'status-' + diffItem.status\">{{ getDiffStatusText(diffItem.status) }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"sub-content\">\n\t\t\t\t\t\t\t<view class=\"sub-info-grid\">\n\t\t\t\t\t\t\t\t<view class=\"sub-info-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub-info-item\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-label\">差价金额：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-value amount\">￥{{ diffItem.diffAmount }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"sub-info-item warranty-info\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-label\">质保日期：</text>\n\t\t\t\t\t\t\t\t\t\t<view class=\"warranty-details\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"sub-value\">{{ formatWarrantyDate(diffItem.partsWarrantyPeriod) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"remaining-days-badge\" v-if=\"diffItem.partsWarrantyPeriod\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text>{{ getRemainingWarrantyDays(diffItem.partsWarrantyPeriod) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"sub-info-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub-info-item reason-item\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-label\">原因：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-value reason\">{{ diffItem.reasonDetail }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"sub-info-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"sub-info-item\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-label\">申请时间：</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"sub-value time\">{{ diffItem.createdTime }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"sub-actions\">\n\t\t\t\t\t\t\t\t<view class=\"cancel-btn\" v-if=\"diffItem.status === 0\"\n\t\t\t\t\t\t\t\t\************=\"showDiffCancelModal(diffItem)\">\n\t\t\t\t\t\t\t\t\t取消差价\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部按钮 -->\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn-cancel\" @click=\"goBack\">取消</view>\n\t\t\t<view class=\"btn-confirm\" @click=\"diffApplyConfirm\">提交申请</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport Upload from '@/components/upload.vue';\n\nexport default {\n\tcomponents: {\n\t\tUpload,\n\t},\n\tdata() {\n\t\treturn {\n\t\t\torderInfo: null,\n\t\t\torderId: '',\n\t\t\tcommissionRate: 0,\n\t\t\tdiffApplyForm: {\n\t\t\t\tdiffAmount: '',\n\t\t\t\treasonType: 1,\n\t\t\t\treasonDetail: '',\n\t\t\t\tpartsWarrantyDuration: '',\n\t\t\t\tpartsimgs: [],\n\t\t\t},\n\t\t\tdiffApplyRules: {\n\t\t\t\tdiffAmount: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入差价金额',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}, {\n\t\t\t\t\tvalidator: (rule, value, callback) => {\n\t\t\t\t\t\treturn value >= 0.01;\n\t\t\t\t\t},\n\t\t\t\t\tmessage: '差价金额必须大于等于0.01',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t\treasonDetail: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入差价原因详情',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t\tpartsWarrantyDuration: [{\n\t\t\t\t\trequired: true,\n\t\t\t\t\tmessage: '请输入质保天数',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}, {\n\t\t\t\t\tvalidator: (rule, value, callback) => {\n\t\t\t\t\t\treturn value > 0;\n\t\t\t\t\t},\n\t\t\t\t\tmessage: '质保天数必须大于0',\n\t\t\t\t\ttrigger: ['blur', 'change']\n\t\t\t\t}],\n\t\t\t},\n\t\t\tpay_typeArr: ['', '待支付', '已支付', '已接单', '上门中', '待服务', '服务中', '已完成', '售后'],\n\t\t}\n\t},\n\tcomputed: {\n\t\tserviceFee() {\n\t\t\tconst diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;\n\t\t\treturn (diffAmount * this.commissionRate / 100).toFixed(2);\n\t\t},\n\t\tactualAmount() {\n\t\t\tconst diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;\n\t\t\tconst serviceFee = parseFloat(this.serviceFee) || 0;\n\t\t\treturn (diffAmount - serviceFee).toFixed(2);\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.$api.shifu.getpercentageOfCommission().then(res=>{\n\t\t\tconsole.log(res)\n\t\t\tif (res.code === \"200\") {\n\t\t\t\tthis.commissionRate = res.data || 0;\n\t\t\t}\n\t\t}).catch(err => {\n\t\t\tconsole.error('获取服务费比例失败:', err);\n\t\t});\n\n\t\tif (options.orderId) {\n\t\t\tthis.orderId = options.orderId;\n\t\t\tthis.loadOrderInfo();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ticon: 'none',\n\t\t\t\ttitle: '订单信息缺失'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.goBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\tonReady() {\n\t\tthis.$nextTick(() => {\n\t\t\tif (this.$refs.diffApplyForm && this.$refs.diffApplyForm.setRules) {\n\t\t\t\tthis.$refs.diffApplyForm.setRules(this.diffApplyRules);\n\t\t\t}\n\t\t});\n\t},\n\tmethods: {\n\t\tformatAmount(amount) {\n\t\t\tconst num = parseFloat(amount) || 0;\n\t\t\treturn num.toFixed(2);\n\t\t},\n\t\tonDiffAmountChange(value) {\n\t\t\tconsole.log('差价金额变化:', value);\n\t\t},\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\tasync loadOrderInfo() {\n\t\t\ttry {\n\t\t\t\tconst orderDetails = uni.getStorageSync('orderdetails');\n\t\t\t\tif (orderDetails && orderDetails.id == this.orderId) {\n\t\t\t\t\tthis.orderInfo = orderDetails;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '订单信息获取失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载订单信息失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '订单信息获取失败'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tgetOrderStatusText(payType) {\n\t\t\treturn this.pay_typeArr[payType] || '未知状态';\n\t\t},\n\t\tgetDiffStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'-1': '已取消',\n\t\t\t\t0: '待确认',\n\t\t\t\t1: '已确认待支付',\n\t\t\t\t2: '已支付',\n\t\t\t\t3: '已拒绝'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知状态';\n\t\t},\n\t\tformatWarrantyDate(timestamp) {\n\t\t\tif (!timestamp) {\n\t\t\t\treturn '无质保信息';\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst date = new Date(timestamp);\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn '无效日期';\n\t\t\t\t}\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('格式化质保日期失败:', error);\n\t\t\t\treturn '格式错误';\n\t\t\t}\n\t\t},\n\t\tgetRemainingWarrantyDays(timestamp) {\n\t\t\tif (!timestamp) return '';\n\t\t\tconst now = new Date();\n\t\t\tconst warrantyDate = new Date(timestamp);\n\t\t\t\n\t\t\tif (isNaN(warrantyDate.getTime()) || warrantyDate < now) {\n\t\t\t\treturn '已过期';\n\t\t\t}\n\t\t\t\n\t\t\tconst diffTime = warrantyDate.getTime() - now.getTime();\n\t\t\tconst diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\t\t\t\n\t\t\treturn `剩余${diffDays}天`;\n\t\t},\n\t\tgetScrollViewHeight(itemCount) {\n\t\t\tconst maxHeight = 450;\n\t\t\tconst itemHeight = 150;\n\t\t\tconst calculatedHeight = Math.min(itemCount * itemHeight, maxHeight);\n\t\t\treturn calculatedHeight;\n\t\t},\n\t\timgUploadDiff(e) {\n\t\t\tconst { imagelist, imgtype } = e;\n\t\t\tthis.$set(this.diffApplyForm, imgtype, imagelist);\n\t\t},\n\t\tshowDiffCancelModal(diffItem) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '取消差价申请',\n\t\t\t\tcontent: `确定要取消差价单号 ${diffItem.diffCode} 的申请吗？`,\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.diffCancel(diffItem);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tasync diffCancel(diffItem) {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.diffCancel({\n\t\t\t\t\tid: diffItem.id\n\t\t\t\t});\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '差价申请已取消',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.loadOrderInfo();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '取消失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tconsole.error('Error in diffCancel:', err);\n\t\t\t}\n\t\t},\n\t\tasync diffApplyConfirm() {\n\t\t\ttry {\n\t\t\t\tif (!this.$refs.diffApplyForm || !this.$refs.diffApplyForm.validate) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '表单未准备就绪，请稍后重试'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tawait this.$refs.diffApplyForm.validate();\n\n\t\t\t\tif (!this.orderInfo) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '订单信息缺失'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst partsImgsString = this.diffApplyForm.partsimgs.map(img => img.path).join(',');\n\n\t\t\t\t\tconst diffAmount = parseFloat(this.diffApplyForm.diffAmount) || 0;\n\t\t\t\t\tconst serviceFee = parseFloat(this.serviceFee) || 0;\n\t\t\t\t\tconst actualDiffAmount = diffAmount - serviceFee;\n\n\t\t\t\t\tconst res = await this.$api.shifu.diffApply({\n\t\t\t\t\t\torderId: this.orderInfo.id,\n\t\t\t\t\t\tdiffAmount: actualDiffAmount,\n\t\t\t\t\t\treasonType: this.diffApplyForm.reasonType,\n\t\t\t\t\t\treasonDetail: this.diffApplyForm.reasonDetail,\n\t\t\t\t\t\tpartsImgs: partsImgsString,\n\t\t\t\t\t\tpartsWarrantyPeriod: parseInt(this.diffApplyForm.partsWarrantyDuration)\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '差价申请成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.goBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '差价申请失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Error in diffApply:', err);\n\t\t\t\t}\n\t\t\t} catch (errors) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请检查填写信息'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tpadding: 24rpx;\n\tbackground-color: #f5f7fa;\n\tmin-height: 100vh;\n\tpadding-bottom: 140rpx;\n}\n\n.content {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 24rpx;\n}\n\n.card {\n\tbackground: #ffffff;\n\tborder-radius: 16rpx;\n\tpadding: 32rpx;\n\tbox-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);\n}\n\n/* 订单信息样式 */\n.order-info {\n\t.order-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 24rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 1rpx solid #f1f2f3;\n\n\t\t.order-no {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #606266;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\t.order-status-tag {\n\t\t\tpadding: 6rpx 16rpx;\n\t\t\tbackground: #ecf5ff;\n\t\t\tcolor: #409eff;\n\t\t\tborder-radius: 20rpx;\n\t\t\tfont-size: 22rpx;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n\n\t.order-detail {\n\t\t.goods-info {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.goods-image {\n\t\t\t\twidth: 120rpx;\n\t\t\t\theight: 120rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t\tbackground: #f5f7fa;\n\t\t\t}\n\n\t\t\t.goods-text {\n\t\t\t\tflex: 1;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tgap: 12rpx;\n\n\t\t\t\t.goods-name {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #303133;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t}\n\n\t\t\t\t.goods-price {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #ff6b35;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* 表单样式 */\n.form-container {\n\t.form-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #303133;\n\t\tmargin-bottom: 32rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 1rpx solid #f1f2f3;\n\t}\n}\n\n.form-item {\n\tmargin-bottom: 32rpx;\n\tpadding-bottom: 24rpx;\n\tborder-bottom: 1rpx solid #f1f2f3;\n\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t\tpadding-bottom: 0;\n\t\tborder-bottom: none;\n\t}\n}\n\n/* 费用明细样式 */\n.fee-detail-container {\n\tmargin: 24rpx 0;\n\tpadding: 28rpx;\n\tbackground: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid #e8f0ff;\n\n\t.fee-detail-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #303133;\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.fee-detail-content {\n\t\t.fee-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 16rpx 0;\n\n\t\t\t.fee-label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #606266;\n\t\t\t}\n\n\t\t\t.fee-value {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\n\t\t\t\t&.primary {\n\t\t\t\t\tcolor: #409eff;\n\t\t\t\t}\n\n\t\t\t\t&.danger {\n\t\t\t\t\tcolor: #f56c6c;\n\t\t\t\t}\n\n\t\t\t\t&.success {\n\t\t\t\t\tcolor: #67c23a;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.total {\n\t\t\t\tpadding-top: 20rpx;\n\t\t\t\tmargin-top: 12rpx;\n\t\t\t\tborder-top: 1rpx dashed #dcdfe6;\n\n\t\t\t\t.fee-label {\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #303133;\n\t\t\t\t}\n\n\t\t\t\t.fee-value {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* 原因类型显示 */\n.reason-type-display {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx;\n\tbackground: #f5f7fa;\n\tborder-radius: 8rpx;\n\n\t.reason-type-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #303133;\n\t\tfont-weight: 500;\n\t}\n\n\t.reason-type-badge {\n\t\tbackground: #409eff;\n\t\tcolor: #fff;\n\t\tfont-size: 22rpx;\n\t\tpadding: 6rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n}\n\n/* 质保输入组 */\n.warranty-input-group {\n\tdisplay: flex;\n\talign-items: center;\n\t\n\t.unit-text {\n\t\tmargin-left: 16rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #606266;\n\t}\n}\n\n/* 上传区域 */\n.upload-container {\n\tmargin-top: 20rpx;\n\tpadding: 32rpx;\n\tbackground: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n\tborder-radius: 16rpx;\n\tborder: 2rpx dashed #94a3b8;\n\tbox-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n\ttransition: all 0.3s ease;\n\n\t&:hover {\n\t\tborder-color: #3b82f6;\n\t\tbackground: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\n\t\ttransform: translateY(-2rpx);\n\t}\n}\n\n/* 差价申请记录 */\n.sub-orders {\n\t.section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 24rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 1rpx solid #f1f2f3;\n\n\t\t.title-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #303133;\n\t\t}\n\n\t\t.title-count {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #909399;\n\t\t\tmargin-left: 12rpx;\n\t\t}\n\t}\n}\n\n.sub-scroll-container {\n\tmax-height: 450rpx;\n}\n\n.sub-item {\n\tpadding: 24rpx;\n\tbackground: #f9fafc;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 20rpx;\n\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.sub-head {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\n\t\t.sub-no {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #606266;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\t.sub-status-tag {\n\t\t\tpadding: 6rpx 16rpx;\n\t\t\tborder-radius: 20rpx;\n\t\t\tfont-size: 22rpx;\n\t\t\tfont-weight: 500;\n\n\t\t\t&.status-0 {\n\t\t\t\tbackground: #fdf6ec;\n\t\t\t\tcolor: #e6a23c;\n\t\t\t}\n\n\t\t\t&.status-1 {\n\t\t\t\tbackground: #ecf5ff;\n\t\t\t\tcolor: #409eff;\n\t\t\t}\n\n\t\t\t&.status-2 {\n\t\t\t\tbackground: #f0f9eb;\n\t\t\t\tcolor: #67c23a;\n\t\t\t}\n\n\t\t\t&.status--1,\n\t\t\t&.status-3 {\n\t\t\t\tbackground: #fef0f0;\n\t\t\t\tcolor: #f56c6c;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sub-content {\n\t\t.sub-info-grid {\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.sub-info-row {\n\t\t\t\tdisplay: flex;\n\t\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\t.sub-info-item {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\t.sub-label {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #909399;\n\t\t\t\t\t\tmargin-right: 12rpx;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t}\n\n\t\t\t\t\t.sub-value {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #606266;\n\n\t\t\t\t\t\t&.amount {\n\t\t\t\t\t\t\tcolor: #ff6b35;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.reason {\n\t\t\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.time {\n\t\t\t\t\t\t\tcolor: #909399;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.warranty-info {\n\t\t\t\t\t\t.warranty-details {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tgap: 12rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&.reason-item {\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\talign-items: flex-start;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.remaining-days-badge {\n\tpadding: 4rpx 12rpx;\n\tbackground: #ffefe6;\n\tcolor: #ff6b35;\n\tborder-radius: 12rpx;\n\tfont-size: 20rpx;\n}\n\n.cancel-btn {\n\tpadding: 12rpx 24rpx;\n\tbackground: #f56c6c;\n\tcolor: #fff;\n\tborder-radius: 8rpx;\n\tfont-size: 24rpx;\n\ttext-align: center;\n}\n\n/* 底部按钮 */\n.footer {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: linear-gradient(180deg, #ffffff 0%, #f9fafb 100%);\n\tpadding: 24rpx 24rpx 32rpx 24rpx;\n\tborder-top: 2rpx solid #e5e7eb;\n\tdisplay: flex;\n\tgap: 24rpx;\n\tbox-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\n\t.btn-cancel,\n\t.btn-confirm {\n\t\tflex: 1;\n\t\theight: 96rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 16rpx;\n\t\tfont-size: 34rpx;\n\t\tfont-weight: 600;\n\t\ttransition: all 0.3s ease;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: -100%;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\n\t\t\ttransition: left 0.5s;\n\t\t}\n\n\t\t&:active::before {\n\t\t\tleft: 100%;\n\t\t}\n\t}\n\n\t.btn-cancel {\n\t\tbackground: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);\n\t\tcolor: #475569;\n\t\tborder: 2rpx solid #cbd5e1;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\n\n\t\t&:active {\n\t\t\tbackground: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);\n\t\t\ttransform: translateY(2rpx);\n\t\t}\n\t}\n\t\n\t.btn-confirm {\n\t\tbackground: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n\t\tcolor: #ffffff;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);\n\n\t\t&:active {\n\t\t\tbackground: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);\n\t\t\ttransform: translateY(2rpx);\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.6);\n\t\t}\n\t}\n}\n\n/* 动画效果 */\n.fade-slide-enter-active,\n.fade-slide-leave-active {\n\ttransition: all 0.3s ease;\n}\n\n.fade-slide-enter-from {\n\topacity: 0;\n\ttransform: translateY(-20rpx);\n}\n\n.fade-slide-leave-to {\n\topacity: 0;\n\ttransform: translateY(-10rpx);\n}\n\n/* 表单元素样式覆盖 */\n/deep/ .u-form-item__body {\n\tpadding: 0 !important;\n}\n\n/deep/ .u-form-item__label {\n\tfont-size: 30rpx !important;\n\tcolor: #303133 !important;\n\tfont-weight: 600 !important;\n\tmargin-bottom: 20rpx !important;\n}\n\n/deep/ .u--input__content,\n/deep/ .u--textarea__content {\n\tbackground: #f5f7fa !important;\n\tborder-radius: 8rpx !important;\n\tpadding: 20rpx !important;\n\tfont-size: 28rpx !important;\n}\n\n/deep/ .u--input__content {\n\theight: 88rpx !important;\n}\n\n/deep/ .u--textarea__content {\n\tmin-height: 160rpx !important;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=style&index=0&id=7ae43980&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./chajia.vue?vue&type=style&index=0&id=7ae43980&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756176205192\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}