{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-column-notice/u-column-notice.vue?ba23", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-column-notice/u-column-notice.vue?917b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-column-notice/u-column-notice.vue?9ca3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-column-notice/u-column-notice.vue?e569", "uni-app:///node_modules/uview-ui/components/u-column-notice/u-column-notice.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-column-notice/u-column-notice.vue?61d2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-column-notice/u-column-notice.vue?1a3c"], "names": ["mixins", "watch", "text", "immediate", "handler", "uni", "computed", "textStyle", "style", "vertical", "data", "index", "methods", "noticeChange", "clickHandler", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2Dl3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACA;IACAC;MACA,iDACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-column-notice/u-column-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-column-notice.vue?vue&type=template&id=515940d7&scoped=true&\"\nvar renderjs\nimport script from \"./u-column-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./u-column-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-column-notice.vue?vue&type=style&index=0&id=515940d7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"515940d7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-column-notice/u-column-notice.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=template&id=515940d7&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.textStyle])\n  var g0 = [\"link\", \"closable\"].includes(_vm.mode)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-notice\"\n\t\t@tap=\"clickHandler\"\n\t>\n\t\t<slot name=\"icon\">\n\t\t\t<view\n\t\t\t\tclass=\"u-notice__left-icon\"\n\t\t\t\tv-if=\"icon\"\n\t\t\t>\n\t\t\t\t<u-icon\n\t\t\t\t\t:name=\"icon\"\n\t\t\t\t\t:color=\"color\"\n\t\t\t\t\tsize=\"19\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t</slot>\n\t\t<swiper\n\t\t\t:disable-touch=\"disableTouch\"\n\t\t\t:vertical=\"step ? false : true\"\n\t\t\tcircular\n\t\t\t:interval=\"duration\"\n\t\t\t:autoplay=\"true\"\n\t\t\tclass=\"u-notice__swiper\"\n\t\t\t@change=\"noticeChange\"\n\t\t>\n\t\t\t<swiper-item\n\t\t\t\tv-for=\"(item, index) in text\"\n\t\t\t\t:key=\"index\"\n\t\t\t\tclass=\"u-notice__swiper__item\"\n\t\t\t>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-notice__swiper__item__text u-line-1\"\n\t\t\t\t\t:style=\"[textStyle]\"\n\t\t\t\t>{{ item }}</text>\n\t\t\t</swiper-item>\n\t\t</swiper>\n\t\t<view\n\t\t\tclass=\"u-notice__right-icon\"\n\t\t\tv-if=\"['link', 'closable'].includes(mode)\"\n\t\t>\n\t\t\t<u-icon\n\t\t\t\tv-if=\"mode === 'link'\"\n\t\t\t\tname=\"arrow-right\"\n\t\t\t\t:size=\"17\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t\t<u-icon\n\t\t\t\tv-if=\"mode === 'closable'\"\n\t\t\t\tname=\"close\"\n\t\t\t\t:size=\"16\"\n\t\t\t\t:color=\"color\"\n\t\t\t\t@click=\"close\"\n\t\t\t></u-icon>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * ColumnNotice 滚动通知中的垂直滚动 内部组件\n\t * @description 该组件用于滚动通告场景，是其中的垂直滚动方式\n\t * @tutorial https://www.uviewui.com/components/noticeBar.html\n\t * @property {Array}\t\t\ttext \t\t\t显示的内容，字符串\n\t * @property {String}\t\t\ticon \t\t\t是否显示左侧的音量图标 （ 默认 'volume' ）\n\t * @property {String}\t\t\tmode \t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\n\t * @property {String}\t\t\tcolor \t\t\t文字颜色，各图标也会使用文字颜色 （ 默认 '#f9ae3d' ）\n\t * @property {String}\t\t\tbgColor \t\t背景颜色 （ 默认 '#fdf6ec' ）\n\t * @property {String | Number}\tfontSize\t\t字体大小，单位px  （ 默认 14 ）\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(rpx)，这有利于控制文字无论多少时，都能有一个恒定的速度 （ 默认 80 ）\n\t * @property {Boolean}\t\t\tstep\t\t\tdirection = row时，是否使用步进形式滚动 （ 默认 false ）\n\t * @property {String | Number}\tduration\t\t滚动一个周期的时间长，单位ms （ 默认 1500 ）\n\t * @property {Boolean}\t\t\tdisableTouch\t是否禁止用手滑动切换   目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序 （ 默认 true ）\n\t * @example \n\t */\n\texport default {\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\twatch: {\n\t\t\ttext: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(newValue, oldValue) {\n\t\t\t\t\tif(!uni.$u.test.array(newValue)) {\n\t\t\t\t\t\tuni.$u.error('noticebar组件direction为column时，要求text参数为数组形式')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 文字内容的样式\n\t\t\ttextStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle.color = this.color\n\t\t\t\tstyle.fontSize = uni.$u.addUnit(this.fontSize)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 垂直或者水平滚动\n\t\t\tvertical() {\n\t\t\t\tif (this.mode == 'horizontal') return false\n\t\t\t\telse return true\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tindex:0\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tnoticeChange(e){\n\t\t\t\tthis.index = e.detail.current\n\t\t\t},\n\t\t\t// 点击通告栏\n\t\t\tclickHandler() {\n\t\t\t\tthis.$emit('click', this.index)\n\t\t\t},\n\t\t\t// 点击关闭按钮\n\t\t\tclose() {\n\t\t\t\tthis.$emit('close')\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-notice {\n\t\t@include flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t&__left-icon {\n\t\t\talign-items: center;\n\t\t\tmargin-right: 5px;\n\t\t}\n\n\t\t&__right-icon {\n\t\t\tmargin-left: 5px;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&__swiper {\n\t\t\theight: 16px;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t\tflex: 1;\n\n\t\t\t&__item {\n\t\t\t\t@include flex;\n\t\t\t\talign-items: center;\n\t\t\t\toverflow: hidden;\n\n\t\t\t\t&__text {\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\tcolor: $u-warning;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=style&index=0&id=515940d7&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=style&index=0&id=515940d7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756169663468\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}